STEP 1: PAYMENT BASICS
----------------------------------------

Payment Reference:
    - CdtTrfTxInf-PmtId-InstrId
    - CdtTrfTxInf-PmtId-EndToEndId[CBPR_RestrictedFINXMax35Text]
    - CdtTrfTxInf-PmtId-TxId[CBPR_RestrictedFINXMax35Text]
    - GrpHdr-MsgId[CBPR_RestrictedFINXMax35Text] (serverOnly)
    - CdtTrfTxInf-PmtId-UETR (serverOnly)
    - CdtTrfTxInf-PmtId-ClrSysRef[CBPR_RestrictedFINXMax35Text] (serverOnly)

Payment Priority:
    - CdtTrfTxInf-PmtTpInf-InstrPrty
○ Normal
● High

Service Level:
    - CdtTrfTxInf-PmtTpInf-SvcLvl-Cd
● URGP (Urgent Payment)   ○ NURG (Non-Urgent)
○ SEPA (SEPA Credit)      ○ SDVA (Same Day Value)

Additional Service Level (Optional):
    - CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry[CBPR_RestrictedFINXMax35Text]

Category Purpose: [SUPP ▼]
    - CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd
    - CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry[CBPR_RestrictedFINXMax35Text]

Clearing Channel:
    - CdtTrfTxInf-PmtTpInf-ClrChanl

Local Instrument:
    - CdtTrfTxInf-PmtTpInf-LclInstrm-Cd
    - CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry[CBPR_RestrictedFINXMax35Text]

Purpose Code: [IVPT ▼]
    - CdtTrfTxInf-Purp-Cd
    - CdtTrfTxInf-Purp-Prtry[CBPR_RestrictedFINXMax35Text]

Further Details:
    - GrpHdr-CreDtTm[CBPR_DateTime] (serverOnly)
    - GrpHdr-NbOfTxs (serverOnly)
----------------------------------------

STEP 2: AMOUNT & CURRENCY DETAILS
----------------------------------------

Instructed Amount: [100,000.00] [USD ▼]
    - CdtTrfTxInf-InstdAmt[CBPR_Amount__1]

Does this payment involve currency conversion? 
○ No, same currency throughout
● Yes, currency conversion is needed

|   Settlement Amount: [90,000.00] [EUR ▼]
|       - CdtTrfTxInf-IntrBkSttlmAmt[CBPR_Amount__1]
|
|   Exchange Rate: [0.9] (automatically calculated from Settlement Amount)
|       - CdtTrfTxInf-XchgRate

Charge Bearer:
    - CdtTrfTxInf-ChrgBr
○ DEBT (Charges paid by sender)
○ CRED (Charges paid by beneficiary)
● SHAR (Charges shared)

Charges Agent:
    - CdtTrfTxInf-ChrgsInf-Agt[BranchAndFinancialInstitutionIdentification6__1]

Charges: [5.00] [EUR ▼]
    - CdtTrfTxInf-ChrgsInf-Amt[CBPR_Amount__1]

----------------------------------------

STEP 3: SETTLEMENT INFORMATION
----------------------------------------

Settlement Method:
    - GrpHdr-SttlmInf-SttlmMtd
○ INDA (Instructed Agent)
● COVE (Coverage - via correspondent)
○ CLRG (Clearing System)

Settlement Date: [21/05/2025]
    - CdtTrfTxInf-IntrBkSttlmDt[ISODate]

Settlement Priority:
    - CdtTrfTxInf-SttlmPrty
○ Normal
● High
○ Urgent

Settlement Time Indication:
    - CdtTrfTxInf-SttlmTmIndctn-DbtDtTm[CBPR_DateTime]
    - CdtTrfTxInf-SttlmTmIndctn-CdtDtTm[CBPR_DateTime]

▼ Settlement Time Requirements (Optional)

|   From Time: [11:00:00]
|       - CdtTrfTxInf-SttlmTmReq-FrTm[CBPR_Time]
|
|   Till Time: [12:00:00]
|       - CdtTrfTxInf-SttlmTmReq-TillTm[CBPR_Time]
|
|   CLS Time: [11:30:00]
|       - CdtTrfTxInf-SttlmTmReq-CLSTm[CBPR_Time]
|
|   Reject Time: [12:00:00]
|       - CdtTrfTxInf-SttlmTmReq-RjctTm[CBPR_Time]

Instructing Reimbursement Agent:
    - GrpHdr-SttlmInf-InstgRmbrsmntAgt[BranchAndFinancialInstitutionIdentification6__1]
    - GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct[CashAccount38__1]

Instructed Reimbursement Agent:
    - GrpHdr-SttlmInf-InstdRmbrsmntAgt[BranchAndFinancialInstitutionIdentification6__1]
    - GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct[CashAccount38__1]

Third Reimbursement Agent:
    - GrpHdr-SttlmInf-ThrdRmbrsmntAgt[BranchAndFinancialInstitutionIdentification6__1]
    - GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct[CashAccount38__1]

Settlement Account:
    - GrpHdr-SttlmInf-SttlmAcct[CashAccount38__1]

----------------------------------------

STEP 4: DEBTOR INFORMATION
----------------------------------------

Debtor Name: [Corporation X]
    - CdtTrfTxInf-Dbtr-Nm[CBPR_RestrictedFINXMax140Text_Extended]

Debtor Address:
    - CdtTrfTxInf-Dbtr-PstlAdr[PostalAddress24__1]

Debtor Identification:
    - CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC[AnyBICDec2014Identifier]
    - CdtTrfTxInf-Dbtr-Id-OrgId-LEI[LEIIdentifier]
    - CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id[CBPR_RestrictedFINXMax35Text]
    - CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd[ExternalOrganisationIdentification1Code]
    - CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr[CBPR_RestrictedFINXMax35Text]
    - CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth[DateAndPlaceOfBirth1__1]
    - CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id[CBPR_RestrictedFINXMax35Text]
    - CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd[ExternalPersonIdentification1Code]
    - CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr[CBPR_RestrictedFINXMax35Text]
    - CdtTrfTxInf-Dbtr-CtryOfRes[CountryCode]

Debtor Account: [**********************]
Debtor Account Currency: [USD ▼]
    - CdtTrfTxInf-DbtrAcct[CashAccount38__1]

▼ Ultimate Debtor (Optional)
|   - CdtTrfTxInf-UltmtDbtr[PartyIdentification135__1]

----------------------------------------

STEP 5: CREDITOR INFORMATION
----------------------------------------

Creditor Name: [Corporation Y]
    - CdtTrfTxInf-Cdtr-Nm[CBPR_RestrictedFINXMax140Text_Extended]

Creditor Address:
    - CdtTrfTxInf-Cdtr-PstlAdr[PostalAddress24__1]

Creditor Country of Residence:
    - CdtTrfTxInf-Cdtr-CtryOfRes[CountryCode]

Creditor Identification:
    - CdtTrfTxInf-Cdtr-Id[Party38Choice__1]

Creditor Account:
    - CdtTrfTxInf-CdtrAcct[CashAccount38__1]

▼ Ultimate Creditor (Optional)
|   - CdtTrfTxInf-UltmtCdtr[PartyIdentification135__1]

----------------------------------------

STEP 6: FINANCIAL INSTITUTION INFORMATION
----------------------------------------

Initiating Party:
    - CdtTrfTxInf-InitgPty[PartyIdentification135__1]

Debtor Agent:
    - CdtTrfTxInf-DbtrAgt[BranchAndFinancialInstitutionIdentification6__1]
    - CdtTrfTxInf-DbtrAgtAcct[CashAccount38__1]

Creditor Agent:
    - CdtTrfTxInf-CdtrAgt-FinInstnId[FinancialInstitutionIdentification18__1]
    - CdtTrfTxInf-CdtrAgt-BrnchId-Id[CBPR_RestrictedFINXMax35Text]
    - CdtTrfTxInf-CdtrAgtAcct[CashAccount38__1]

Instructing Agent:
    - CdtTrfTxInf-InstgAgt[BranchAndFinancialInstitutionIdentification6__2]

Previous Instructing Agent 1:
    - CdtTrfTxInf-PrvsInstgAgt1[BranchAndFinancialInstitutionIdentification6__1]
    - CdtTrfTxInf-PrvsInstgAgt1Acct[CashAccount38__1]

Previous Instructing Agent 2:
    - CdtTrfTxInf-PrvsInstgAgt2[BranchAndFinancialInstitutionIdentification6__1]
    - CdtTrfTxInf-PrvsInstgAgt2Acct[CashAccount38__1]

Previous Instructing Agent 3:
    - CdtTrfTxInf-PrvsInstgAgt3[BranchAndFinancialInstitutionIdentification6__1]
    - CdtTrfTxInf-PrvsInstgAgt3Acct[CashAccount38__1]

Instructed Agent:
    - CdtTrfTxInf-InstdAgt[BranchAndFinancialInstitutionIdentification6__2]

Instructions:
    - CdtTrfTxInf-InstrForCdtrAgt-Cd
    - CdtTrfTxInf-InstrForCdtrAgt-InstrInf[CBPR_RestrictedFINXMax140Text]
    - CdtTrfTxInf-InstrForNxtAgt-InstrInf[CBPR_RestrictedFINXMax35Text]

▼ Intermediary Agents
|
|   Intermediary Agent 1:
|       - CdtTrfTxInf-IntrmyAgt1[BranchAndFinancialInstitutionIdentification6__1]
|       - CdtTrfTxInf-IntrmyAgt1Acct[CashAccount38__1]
|
|   Intermediary Agent 2:
|       - CdtTrfTxInf-IntrmyAgt2[BranchAndFinancialInstitutionIdentification6__1]
|       - CdtTrfTxInf-IntrmyAgt2Acct[CashAccount38__1]
|
|   Intermediary Agent 3:
|       - CdtTrfTxInf-IntrmyAgt3[BranchAndFinancialInstitutionIdentification6__1]
|       - CdtTrfTxInf-IntrmyAgt3Acct[CashAccount38__1]

----------------------------------------

STEP 7: REMITTANCE INFORMATION
----------------------------------------

Remittance Information Type:
● Unstructured
○ Structured

Unstructured Information: [Invoice 123]
    - CdtTrfTxInf-RmtInf-Ustrd[CBPR_RestrictedFINXMax140Text_Extended]

Structured Information:
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-RltdDt[ISODate]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt[ISODate]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt[ActiveOrHistoricCurrencyAndAmount]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt[DiscountAmountAndType1__1]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt[ActiveOrHistoricCurrencyAndAmount]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt[TaxAmountAndType1__1]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn[DocumentAdjustment1__1]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt[ActiveOrHistoricCurrencyAndAmount]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt[ActiveOrHistoricCurrencyAndAmount]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt[DiscountAmountAndType1__1]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt[ActiveOrHistoricCurrencyAndAmount]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt[TaxAmountAndType1__1]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn[DocumentAdjustment1__1]
    - CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt[ActiveOrHistoricCurrencyAndAmount]
    - CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd
    - CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-Invcr[PartyIdentification135__4]
    - CdtTrfTxInf-RmtInf-Strd-Invcee[PartyIdentification135__4]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr[TaxParty2__1]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr[TaxParty2__1]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb[CBPR_RestrictedFINXMax140Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt[ActiveOrHistoricCurrencyAndAmount]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt[ActiveOrHistoricCurrencyAndAmount]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dt[ISODate]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-SeqNb
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd[TaxPeriod2]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt[ActiveOrHistoricCurrencyAndAmount]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt[ActiveOrHistoricCurrencyAndAmount]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd[TaxPeriod2]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt[ActiveOrHistoricCurrencyAndAmount]
    - CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf[CBPR_RestrictedFINXMax140Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd
    - CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr[CBPR_RestrictedFINXMax35Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee[PartyIdentification135__4]
    - CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr[PartyIdentification135__4]
    - CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RefNb[CBPR_RestrictedFINXMax140Text_Extended]
    - CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Dt[ISODate]
    - CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt[ActiveOrHistoricCurrencyAndAmount]
    - CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd[TrueFalseIndicator]
    - CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd[TrueFalseIndicator]
    - CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf[CBPR_RestrictedFINXMax140Text_Extended]

▼ Related Remittance Information (Optional)

|   Is additional remittance information being sent separately?
|   ○ No   ● Yes
|
|   Remittance ID: [abc-123]
|       - CdtTrfTxInf-RltdRmtInf-RmtId[CBPR_RestrictedFINXMax35Text_Extended]
|
|   Remittance Location Method: [EMAL ▼]
|       - CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd
|   
|   Electronic Address: [<EMAIL>]
|       - CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr
|
|   Postal Address:
|       - CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm[CBPR_RestrictedFINXMax140Text_Extended]
|       - CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr[PostalAddress24__1]

----------------------------------------

STEP 8: Regulatory Reporting
----------------------------------------

Debit/Credit Reporting Indicator:
○ DEBT   ● CRED   ○ BOTH
    - CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd

Authority Name: [Entity 1]
    - CdtTrfTxInf-RgltryRptg-Authrty-Nm[CBPR_RestrictedFINXMax140Text]

Authority Country: [United States ▼]
    - CdtTrfTxInf-RgltryRptg-Authrty-Ctry[CountryCode]

▼ Reporting Details (Optional)
|   - CdtTrfTxInf-RgltryRptg-Dtls-Tp[CBPR_RestrictedFINXMax35Text]
|   - CdtTrfTxInf-RgltryRptg-Dtls-Dt[ISODate]
|   - CdtTrfTxInf-RgltryRptg-Dtls-Ctry[CountryCode]
|   - CdtTrfTxInf-RgltryRptg-Dtls-Cd
|   - CdtTrfTxInf-RgltryRptg-Dtls-Amt[CBPR_Amount__1]
|   - CdtTrfTxInf-RgltryRptg-Dtls-Inf[CBPR_RestrictedFINXMax35Text]

----------------------------------------

STEP 9: REVIEW & SUBMIT
----------------------------------------

PAYMENT SUMMARY

Payment Basics [Edit ↗]
- Reference: PAYMENT-2025-123456
- Priority: High
- Service Level: URGP (Urgent Payment), G001 (GPI)
- Category Purpose: SUPP (Supplier Payment)
- Purpose: IVPT (Invoice Payment)

Amount & Currency [Edit ↗]
- Instructed Amount: 1,000.00 USD
- Interbank Settlement Amount: 900.00 EUR
- Exchange Rate: 0.9
- Charge Bearer: DEBT
- Charges: 5.00 EUR

Settlement Information [Edit ↗]
- Method: COVE (Coverage)
- Date: 21/05/2025
- Priority: High
- Time Requirements: From 11:00 to 12:00, CLS 11:30

Debtor Details [Edit ↗]
- Name: Corporation X
- Location: 123 Main Street, Freiburg, Germany
- Account: ********************** (USD)
- Ultimate Debtor: Customer X

Creditor Details [Edit ↗]
- Name: Corporation Y
- Location: 456 High Street, London, United Kingdom
- Account: ********************** (USD)
- Ultimate Creditor: Customer Y

Financial Institutions [Edit ↗]
- Debtor Agent: BANKAUAAXXX
- Creditor Agent: BANKGBCCXXX
- Reimbursement Agents: RMAAXXXXXXX, RMABXXXXXXX

Remittance Information [Edit ↗]
- Unstructured: Invoice 123
- Related Remittance ID: abc-123
- Sent via: <NAME_EMAIL>
- Regulatory Reporting: CRED, Entity 1 (US)

VALIDATION SUMMARY
✓ All required fields are complete
✓ All identifiers are valid
⚠ Exchange rate differs from market rate by >2% (Please confirm)

[ Save as Draft ]   [ Submit Payment ]
----------------------------------------