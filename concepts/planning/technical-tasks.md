# ISO20022 Message Builder - Technical Tasks (182 Story Points)

## Cross-Cutting Concerns & Infrastructure (10 points)

### Project Setup & Architecture (10 points)

- **INFRA-001**: Set up repository structure in Helaba environment with message-builder-client, message-builder-lib, message-builder-server, and validation-service (5 points)
- **INFRA-002**: Set up services in Docker (5 points)

## Rule Generation Library (NPM Package) (3 points)

### Core Rule Engine (2 points)

- **LIB-001**: Define TypeScript interfaces for custom rule definition format (done, 0 points)
- **LIB-002**: Create script to extract validation rules from JSON schema and merge them with custom rules (mostly done, 2 points)

### Angular Integration Components (1 point)

- **LIB-003**: Create Angular directive for applying generated rules to reactive forms (done, 1 point)
- **LIB-004**: Build error component for displaying validation errors (done, 0 points)

## Angular Frontend (63 points)

### Core Infrastructure (6 points)

- **FE-001**: Set up Angular project and configure routing (done, 0 points)
- **FE-002**: Configure Angular i18n with German translations (mostly done, 1 point)
- **FE-003**: Configure Open API generation (mostly done, 2 points)
- **FE-004**: Implement global error handling and user notification system (3 points)
- **FE-005** (_Additional feature_): Adapt styling to other Helaba applications

### Form Components & Validation (19 points)

- **FE-006**: Create base form input components (text, date, array) (mostly done, 1 point)
- **FE-007**: Build form structure components (one of selector, hidden section) (mostly done, 1 point)
- **FE-008**: Implement form navigation component (stepper/tabs) with progress tracking (started, 3 points)
- **FE-009**: Build validation summary component showing all errors across form sections (5 points)
- **FE-010**: Create success/confirmation page with download functionality (3 points)
- **FE-011**: Implement real-time validation and error display (mostly done, 1 point)
- **FE-012**: Implement file upload for XML/JSON templates that automatically fills out the respective fields (5 points)

### Message-Specific Pages (30 points)

- **FE-013**: Extract custom rules from pacs008 usage guidelines and translate error messages (started, 5 points)
- **FE-014**: Create pacs.008 message form page with all required sections (started, 3 points)
- **FE-015**: Extract custom rules from pacs004 usage guidelines and translate error messages (5 points)
- **FE-016**: Create pacs.004 message form page with pacs.008 upload functionality (3 points)
- **FE-017**: Extract custom rules from pacs002 usage guidelines and translate error messages (3 points)
- **FE-018**: Create pacs.002 message form page (3 points)
- **FE-019**: Extract custom rules from pacs009 usage guidelines and translate error messages (3 points)
- **FE-020**: Create pacs.009 message form page (3 points)
- **FE-021**: Implement message selection/routing page (2 points)

### User Experience Features (3 points)

- **FE-022**: Implement loading indicators for form submissions and API calls (3 points)
- **FE-023** (_Additional feature_): Create tooltip/help system for complex fields
- **FE-024** (_Additional feature_): Build keyboard navigation support
- **FE-025** (_Additional feature_): Implement form data clearing functionality (section and full form)
- **FE-026** (_Additional feature_): Create draft save/load functionality with local storage or API integration

### Data Integration (5 points)

- **FE-027**: Connect to BFF for validation and message creation (5 points)
- **FE-028** (_Additional feature_): Build search components for addresses and IBANs with autocomplete

## Backend for Frontend (Spring Boot) (56 points)

### Core API Infrastructure (13 points)

- **BFF-001**: Set up Spring Boot project with Web, Validation, and HTTP Client dependencies (started, 3 points)
- **BFF-002**: Configure REST API endpoints with proper HTTP methods and status codes (started, 3 points)
- **BFF-003**: Implement request/response DTOs for all API endpoints (3 points)
- **BFF-004**: Set up exception handling with proper error responses (3 points)
- **BFF-005**: Implement CORS configuration for Angular frontend (mostly done, 1 points)

### Message Processing Endpoints (25 points)

- **BFF-006**: Create endpoint for pacs.008 message creation and validation (5 points)
- **BFF-007**: Create endpoint for pacs.004 message creation with pacs.008 reference handling (3 points)
- **BFF-008**: Create endpoint for pacs.002 message creation and validation (3 points)
- **BFF-009**: Create endpoint for pacs.009 message creation and validation (3 points)
- **BFF-010**: Implement input sanitization (3 points)
- **BFF-011**: Implement server-side field population (UUIDs, timestamps, system-generated data) using mocked data (3 points)
- **BFF-012** (_Additional feature_): Implement server-side field population (UUIDs, timestamps, system-generated data) using data from external systems
- **BFF-013**: Integrate XML generation library for final message output (5 points)

### External System Integration (_Additional feature_)

- **BFF-014** (_Additional feature_): Create endpoint for looking up addresses
- **BFF-015** (_Additional feature_): Integrate address lookup endpoint with CEDA
- **BFF-016** (_Additional feature_): Create endpoint for looking up IBANs
- **BFF-017** (_Additional feature_): Integrate IBAN lookup endpoint with CEDA
- **BFF-018** (_Additional feature_): Implement caching layer for external service responses

### Validation Integration (18 points)

- **BFF-019**: Connect BFF with Validation Microservice (5 points)
- **BFF-020**: Validate message against JSON Schema validation endpoint (mostly done, 1 point)
- **BFF-021**: Validate message against custom rules validation endpoint (started, 2 points)
- **BFF-022** (_Additional feature_): Validate relevant fields against field validation endpoint
- **BFF-023**: Build validation result processing and error mapping (5 points)
- **BFF-024**: Add endpoint to validate that template data fits to the message type the user wants to create and send back the flattened object. (5 points)

## Validation Microservice (Spring Boot) (14 points)

### Core Validation Infrastructure (8 points)

- **VAL-001**: Set up Spring Boot microservice (started, 2 points)
- **VAL-002**: Configure JSON Schema validation library integration (done, 0 points)
- **VAL-003**: Implement custom rule engine for business rule validation (started, 3 points)
- **VAL-004**: Create validation result standardization and error reporting (3 points)

### Validation Endpoints (6 points)

- **VAL-005**: Create endpoint for JSON Schema validation with detailed error reporting (started, 3 points)
- **VAL-006**: Create endpoint for custom business rules validation (3 points)
- **VAL-007** (_Additional feature_): Create endpoint for external system validation (IBAN, address verification)
- **VAL-008** (_Additional feature_): Implement combined validation endpoint

## Documentation (3 points)

- **DOC-001**: Create comprehensive documentation for rule definition format and rule generation pipeline and how to use the generated files in a client application (3 points)

## Testing Strategy (10 points)

- **TEST-001**: End-to-end testing for complete user workflows (5 points)
- **TEST-002**: Integration tests for BFF with Validation Microservice (5 points)

## Deployment & Operations (23 points)

- **DEPLOY-001**: Configure build pipeline for the message-builder-lib NPM package (5 points)
- **DEPLOY-002**: Set up CI/CD pipeline with automated testing and deployment (5 points)
- **DEPLOY-003**: Set up deployment for DEV, TEST, and PROD (8 points)
- **DEPLOY-004**: Configure environment-specific configuration management (dev, staging, prod) (5 points)
- **DEPLOY-005** (_Additional feature_): Secure client and endpoints with Helaba SSO
