# ISO20022 Message Builder - Timeline

## 08.08.2025

### Technical tasks (99 Story Points)

#### done

- LIB-001 (0 points)
- LIB-004 (0 points)
- FE-001 (0 points)

#### 04.07.2025 (Client - Server Communication)

- FE-003 (2 points)
- FE-009 (5 points)
- FE-027 (5 points)

#### 11.07.2025 (Form improvements)

- LIB-002 (2 points)
- LIB-003 (1 point)
- FE-006 (1 point)
- FE-007 (1 point)
- FE-008 (3 points)
- FE-010 (3 points)

#### 18.07.2025 (Finish form)

- FE-013 (5 points)
- FE-014 (3 points)

#### 25.07.2025 (Start working on deployment)

- INFRA-001 (5 points)
- INFRA-002 (5 points)
- Basic Deployment

#### 01.08.2025 (Finish deployment and polishing)

- FE-004 (3 points)
- FE-002 (1 point)
- FE-011 (1 point)
- Navigate between form sections using the stepper
- Show "back" button on submit page
- Show set values (max. ca. 5) on collapsed sections in review page, remove preview upon opening
- Automatically scroll up to errors when clicking "Next"
- Automatically create enums from JSON schema

#### 08.08.2025 (buffer)

#### Done

- VAL-002 (0 points)

#### 04.07.2025 (Server basic)

- BFF-001 (3 points)
- BFF-002 (3 points)
- BFF-003 (3 points)
- BFF-005 (1 point)
- VAL-001 (2 points)
- VAL-003 (3 points)

#### 11.07.2025 (Data handling)

- BFF-019 (5 points)
- BFF-004 (3 points)
- BFF-020 (1 point)
- BFF-021 (2 points)
- BFF-023 (5 points)
- VAL-004 (3 points)
- VAL-005 (3 points)

- VAL-006 (3 points)

#### 18.07.2025

- BFF-011 (3 points)
- BFF-013 (5 points)

#### 25.07.2025

- BFF-006 (5 points)

### User stories

- USER-STORY-001 (define rules)
- USER-STORY-002 (generate rules from schema)
- USER-STORY-003 (German translations)
- USER-STORY-004 (pacs.008)
- USER-STORY-009 (server fills out fields)
- USER-STORY-010 (validate input)
- USER-STORY-011 (display errors)
- USER-STORY-012 (summary of errors across pages)
- USER-STORY-013 (navigate between form sections)
- USER-STORY-015 (confirmation for message creation)
- USER-STORY-016 (download message)
- USER-STORY-017 (error message for failed validation on submitted data)

## 12.09.2025

### Technical tasks (83 Story Points)

- FE-012 (5 points) (template upload)
- FE-015 (5 points) (pacs.004)
- FE-016 (3 points) (pacs.004)
- FE-017 (3 points) (pacs.002)
- FE-018 (3 points) (pacs.002)
- FE-019 (3 points) (pacs.009)
- FE-020 (3 points) (pacs.009)
- FE-021 (2 points) (message selection/routing page)
- FE-022 (3 points) (loading indicators)
- BFF-007 (3 points) (pacs.004 endpoint)
- BFF-008 (3 points) (pacs.002 endpoint)
- BFF-009 (3 points) (pacs.009 endpoint)
- BFF-010 (3 points) (input sanitization)
- BFF-024 (5 points) (validate template data)
- DOC-001 (3 points) (documentation for rule definition)
- TEST-001 (5 points) (e2e tests)
- TEST-002 (5 points) (integration tests)
- DEPLOY-001 (5 points) (build pipeline for NPM package)
- DEPLOY-002 (5 points) (CI/CD for testing and deployment)
- DEPLOY-003 (8 points) (deployment on DEV, TEST, and PROD)
- DEPLOY-004 (5 points) (environment variable config)

### User stories

- USER-STORY-005 (pacs.004)
- USER-STORY-006 (pacs.002)
- USER-STORY-007 (pacs.009)
- USER-STORY-008 (template upload)
- USER-STORY-014 (loading indicators)

## Additional features

### Technical tasks

- FE-005 (adapt styling)
- FE-023 (tooltips)
- FE-024 (keyboard navigation)
- FE-025 (form clearing)
- FE-026 (save/load draft)
- FE-028 (search for addresses and IBANs via external systems)
- BFF-012 (populate serverOnly fields from external systems)
- BFF-014 (endpoint for addresses)
- BFF-015 (address lookup with CEDA)
- BFF-016 (endpoint for IBANs)
- BFF-017 (IBAN lookup with CEDA)
- BFF-018 (caching for external responses)
- BFF-022 (validate fields against external services)
- VAL-007 (endpoint for validating against external services)
- VAL-008 (combined validation endpoint)
- DEPLOY-005 (SSO)

### User stories

- USER-STORY-018 (auth)
- USER-STORY-019 (search for addresses/IBANs)
- USER-STORY-020 (save/load draft)
- USER-STORY-021 (tooltips)
- USER-STORY-022 (keyboard navigation)
- USER-STORY-023 (form clearing)
- USER-STORY-024 (style similar to other Helaba apps)
- USER-STORY-025 (error message for failed validation against external services)
