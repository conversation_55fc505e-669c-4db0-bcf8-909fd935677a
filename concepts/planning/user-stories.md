# ISO20022 Message Builder - User Stories

## MVP

### Developer Experience & Setup

- **USER-STORY-001**: As a developer, I want to use a standardized custom rule format, so usage guidelines can be expressed structurally and used programmatically.
- **USER-STORY-002**: As a developer, I want to input a JSON Schema, XSD, and a file of usage guideline rules, and run a CLI command to produce a unified ruleset for a new version of input files or for a new message type.

### Internationalization

- **USER-STORY-003**: As a user, I want to see German translations for all text including error messages.

### Message Creation

- **USER-STORY-004**: As a user, I want to be able to create a pacs.008 message.
- **USER-STORY-005**: As a user, I want to be able to create a pacs.004 message and to upload the original pacs.008 message in that process.
- **USER-STORY-006**: As a user, I want to be able to create a pacs.002 message.
- **USER-STORY-007**: As a user, I want to be able to create a pacs.009 message.
- **USER-STORY-008**: As a user, I want to upload an XML or JSON file, e.g. a previously created message, to prefill fields in the form, so I don't have to retype data.
- **USER-STORY-009**: As a user, I do not want to fill out fields that are easier filled out by the server (e.g. UUIDs) (the data might be provided by an external service).

### Validation & Error Handling

- **USER-STORY-010**: As a user, I want my input to be validated in real-time as I fill out the form.
- **USER-STORY-011** As a user, I want validation errors to be displayed next to the relevant fields.
- **USER-STORY-012**: As a user, I want to see a summary of all validation errors across pages before submitting, so I can go back to the respective pages and fix them there.

### System Feedback & Final Output

- **USER-STORY-013**: As a user, I want to navigate between different sections of a long form easily (e.g. tabs, stepper). This should also serve as a progress indicator showing how much of the form I've completed.
- **USER-STORY-014**: As a user, I want to see loading indicators when the system is processing my request.
- **USER-STORY-015**: As a user, I want to see confirmation when my message has been successfully created.
- **USER-STORY-016**: As a user, I want to download the final message in XML format.
- **USER-STORY-017**: As a user, I want to receive an error message when validation of the submitted data fails because the data does not adhere to the JSON schema or to the usage guideline rules.

## Additional features

### Security

- **USER-STORY-018**: As a system administrator, I want to be sure that only authorized Helaba users can access the client application and the exposed endpoints as soon as allow accessing user data via CEDA.

### Data Management

- **USER-STORY-019**: As a user, I want to search for existing addresses or IBANs as I fill out a form, so I can select from known entries.
- **USER-STORY-020**: As a user, I want to save my progress as a draft, so I can continue working on a message later without losing my work.

### User Experience Enhancements

- **USER-STORY-021**: As a user, I want to see helpful tooltips or guidance text for complex fields, so I understand what information is required.
- **USER-STORY-022**: As a user, I want to use keyboard shortcuts to navigate the form efficiently.
- **USER-STORY-023**: As a user, I want to clear all data in a form section or in the entire form with one action.
- **USER-STORY-024**: As a user, I want the styling and feel of the app to be similar to other applications in the Helaba environment
- **USER-STORY-025**: As a user, I want to receive an error message when validation of the submitted against an external service fails (e.g. for IBANs).
