I am building an app that lets users construct ISO20022 messages, starting with pacs008 but then other formats like pacs004, pacs002, and pacs009 are also relevant. Some more context:

- Auth is not relevant for now
- There needs to be a pipeline that handles generation of rules. The idea is to give as input the JSON schema and XSD schema for a message + custom user defined rules that adhere to a self-defined rule definition. These custom user defined rules are meant to enforce the rules laid out in the usage guidelines of the respective message format. These are in textual format and need to be put into structure (our rule definition). The rule generation takes place in a Lib-Project that is an NPM package.
- We want to build all relevant form components and helpers for the Angular frontend (e.g. directive that enforces the rules, error component that displays errors). The helpers for the form validation (directive, error component) sit in the lib, the form components sit in the frontend.
- We want to start with pacs008 but adding a new message format later on should be relatively simple: Add relevant JSON schema and XSD into input folder in lib, translate usage guidelines into rule definitions, run script to generate combined rules, add a new page in the Angular client, use the directive from the lib and the generated rules from the lib to validate the form. Use components present in the client to build the form.
- There should be Angular i18n set up to translate all text in the app, including error messages.
- We want to build a backend for our frontend (Spring Boot) that takes all requests that require further processing: 1) Requests for information from external systems (e.g. search the database for existing addresses or IBANs, having the frontend do this is also a bit of work), the backend for frontend should connect to external systems that provide this information and expose it to the client 2) Request to add server-only fields, validate the user input and create the final message: The BFF adds some specific fields that cannot be filled out by the user (e.g. a UUID) either with logic present in the backend or from an external system. For security reasons, the backend for frontend also validates the input before creating the final message. For this, it calls a Validation Microservice (Spring Boot), that offers endpoints for checking a message against a JSON schema, for checking a message against custom rules, and an endpoint for checking certain inputs against external systems (e.g. an IBAN). As the JSON schema is converted into rules, in theory the user input should already adhere to the JSON schema when sent to the backend, but to be sure we check the entire input directly against the JSON schema (instead of against the generated rules). The message is also again checked against the custom rules via the Validation Microservice.
  Finally, this second endpoint in the BFF either returns an error to the client if some validation failed, or it uses an existing library to create the final XML message.
- Users should be able to upload an XML or JSON file (e.g. the relevant pacs008 message when creating a pacs004 messsage) and the respective fields should automatically be filled out.
