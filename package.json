{"name": "client", "version": "0.0.0", "scripts": {"prestart": "npm run prepare-generated-assets", "start": "ng serve", "prebuild": "npm run prepare-generated-assets", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "prepare-generated-assets": "npm run generate-form-metadata && npm run generate-api", "generate-api": "ng-openapi-gen", "generate-form-metadata": "ts-node -P scripts/tsconfig.scripts.json scripts/generate-form-metadata.ts", "update-i18n": "npm run internal:extract-i18n && npm run internal:merge-i18n", "internal:extract-i18n": "ng extract-i18n --output-path src/assets/i18n", "internal:merge-i18n": "ts-node -P scripts/tsconfig.scripts.json scripts/merge-xlf.ts --i18n-folder src/assets/i18n --target-langs de", "dev:update-lib": "rm -rf node_modules package-lock.json && npm i && npm run prepare-generated-assets && npm run update-i18n", "dev:apply-translation-proposals": "ts-node -P scripts/tsconfig.scripts.json scripts/apply-translation-proposals.ts --i18n-folder src/assets/i18n --target-langs de"}, "private": true, "dependencies": {"@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@helaba/iso20022-lib": "file:../iso20022-angular-lib/dist/helaba-iso20022-lib-0.0.1.tgz", "@primeng/themes": "^19.1.3", "clsx": "^2.1.1", "primeicons": "^7.0.0", "primeng": "^19.1.3", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.9", "@angular/cli": "^19.2.9", "@angular/compiler-cli": "^19.2.0", "@angular/localize": "^19.2.14", "@types/jasmine": "~5.1.0", "@types/minimist": "^1.2.5", "@xmldom/xmldom": "^0.9.8", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "minimist": "^1.2.8", "ng-openapi-gen": "^0.53.0", "ts-node": "^10.9.2", "typescript": "~5.7.2", "xml-formatter": "^3.6.6", "zod": "^3.25.75"}}