import * as fs from 'fs';
import * as path from 'path';
import {
  DOM<PERSON>arser,
  XMLSerializer,
  Element as XmlElement,
  Document as XmlDocument,
  Node as XmlNode,
  CDATASection,
} from '@xmldom/xmldom';
import minimist from 'minimist';
import { ERROR_MESSAGE_TRANSLATION_PROPOSALS } from '../src/app/generated/pacs008-error-message-translation-proposals';
import { formatXml } from './utils';

const parser = new DOMParser();

const serializer = new XMLSerializer();

const ELEMENT_NODE = 1;
const TEXT_NODE = 3;
const CDATA_SECTION_NODE = 4;

function isElement(node: XmlNode): node is XmlElement {
  return node.nodeType === ELEMENT_NODE;
}

function isCdataSection(node: XmlNode): node is CDATASection {
  return node.nodeType === CDATA_SECTION_NODE;
}

function removeNamespacesRecursively(node: XmlNode): void {
  if (node.nodeType === ELEMENT_NODE) {
    const el = node as XmlElement;
    // Remove the namespace URI
    (el as any).namespaceURI = null;

    // Recursively remove from children
    for (let i = 0; i < el.childNodes.length; i++) {
      removeNamespacesRecursively(el.childNodes[i]);
    }
  }
}

/**
 * Parse command line arguments
 */
function parseArgs(): { i18nFolder: string; targetLangs: string[] } {
  const argv = minimist(process.argv.slice(2), {
    string: ['i18n-folder', 'target-langs'],
    default: {
      i18nFolder: '',
      targetLangs: ['de'],
    },
  });

  return {
    i18nFolder: argv['i18n-folder'],
    targetLangs: argv['target-langs']
      .split(',')
      .map((lang: string) => lang.trim()),
  };
}

function serializeInnerXml(element: XmlElement): string | undefined {
  if (!element || element.nodeType !== ELEMENT_NODE) {
    return undefined;
  }

  const serializer = new XMLSerializer();
  const outputParts: string[] = [];

  for (let child = element.firstChild; child; child = child.nextSibling) {
    if (child.nodeType === ELEMENT_NODE) {
      // Clone to a new document
      const nsFreeDoc = new DOMParser().parseFromString(
        '<root/>',
        'application/xml'
      );
      const imported = nsFreeDoc.importNode(child, true);
      removeNamespacesRecursively(imported);
      outputParts.push(serializer.serializeToString(imported));
    } else if (child.nodeType === TEXT_NODE) {
      outputParts.push(child.nodeValue || '');
    } else if (isCdataSection(child)) {
      outputParts.push(`<![CDATA[${child.data}]]>`);
    }
  }

  return outputParts.join('');
}

function removeNodeFromUnit(transUnit: XmlElement, nodeName: string): void {
  const node = transUnit.getElementsByTagName(nodeName)[0];
  if (node) {
    transUnit.removeChild(node);
  }
}

function addNodeToUnit(
  targetDoc: XmlDocument,
  transUnit: XmlElement,
  nodeName: string,
  textContent: string,
  afterNode: string
) {
  const afterElement = transUnit.getElementsByTagName(afterNode)[0];
  if (!afterElement) {
    throw new Error(`No <${afterNode}> found in unit.`);
  }

  const newNode = targetDoc.createElement(nodeName);

  // Manually split xmlContent into text + <x> nodes
  const parts = textContent.split(/(<x[^>]+\/>)/g);

  for (const part of parts) {
    if (!part.trim()) continue;

    if (part.startsWith('<x')) {
      // Parse the <x ... /> as a separate document
      const xDoc = new DOMParser().parseFromString(
        `<root>${part}</root>`,
        'application/xml'
      );
      const xEl = xDoc.documentElement?.firstChild;

      if (xEl?.nodeName === 'x') {
        const imported = targetDoc.importNode(xEl, true);
        newNode.appendChild(imported);
      } else {
        console.warn('Could not parse <x> node:', part);
        newNode.appendChild(targetDoc.createTextNode(part));
      }
    } else {
      // Regular text
      newNode.appendChild(targetDoc.createTextNode(part));
    }
  }

  if (afterElement.nextSibling) {
    transUnit.insertBefore(newNode, afterElement.nextSibling);
  } else {
    transUnit.appendChild(newNode);
  }
}

/**
 * Replaces `{` and `}` in a string with <x> placeholders for Angular XLF files.
 * Each brace is replaced with an XML tag like <x id="PH" equiv-text="&quot;{&quot;" />
 *
 * @param input - The raw string to convert
 * @returns A string containing <x> tags in place of literal braces
 */
function escapeBracesWithXliffPlaceholders(
  input: string | undefined
): string | undefined {
  if (typeof input === 'undefined') {
    return undefined;
  }
  let placeholderCount = 0;
  return input.replace(/[{}]/g, (match) => {
    const id = placeholderCount === 0 ? 'PH' : `PH_${placeholderCount}`;
    placeholderCount++;
    const equivText = match === '{' ? '&quot;{&quot;' : '&quot;}&quot;';
    return `<x id="${id}" equiv-text="${equivText}" />`;
  });
}

function createNewUnit(
  targetFile: string,
  sourceUnitId: string,
  sourceUnit: XmlElement,
  targetDoc: XmlDocument
): XmlElement {
  console.log(
    `➕ Adding new unit to ${path.basename(targetFile)}: ${sourceUnitId}`
  );

  const newUnit = sourceUnit;

  const target = newUnit.getElementsByTagName('target')[0];

  if (!target) {
    const sourceElement = newUnit.getElementsByTagName('source')[0];
    if (!sourceElement) {
      throw new Error(
        `No <source> found in unit with id ${sourceUnitId} in source file`
      );
    }
    const targetText = `TODO: translate (${sourceElement.textContent || ''})`;

    addNodeToUnit(targetDoc, newUnit, 'target', targetText, 'source');
  }

  const targetProposal = newUnit.getElementsByTagName('target-proposal')[0];
  if (!targetProposal) {
    const targetProposalText = escapeBracesWithXliffPlaceholders(
      ERROR_MESSAGE_TRANSLATION_PROPOSALS[sourceUnitId]
    );
    if (targetProposalText) {
      addNodeToUnit(
        targetDoc,
        newUnit,
        'target-proposal',
        targetProposalText,
        'source'
      );
    }
  }

  return newUnit;
}

function mergeExistingUnit(
  translatedFile: XmlDocument,
  existingTranslatedTransUnit: XmlElement,
  originalTransUnitId: string,
  originalTransUnitClone: XmlElement
): XmlElement {
  // Make sure the <source> and the <context-group> are still the same. Otherwise, take the (updated) <source> and <context-group> from the sourceUnit.
  const originalSource =
    originalTransUnitClone.getElementsByTagName('source')[0];
  const originalSourceContent = serializeInnerXml(originalSource);
  const originalContextGroup =
    originalTransUnitClone.getElementsByTagName('context-group')[0];
  if (!originalSource || !originalContextGroup) {
    throw new Error(
      `No <source> or no <context-group> found in unit with id ${existingTranslatedTransUnit.getAttribute(
        'id'
      )} in original file`
    );
  }
  const serializedOriginalContextGroup =
    serializer.serializeToString(originalContextGroup);

  const existingTranslatedSource =
    existingTranslatedTransUnit.getElementsByTagName('source')[0];
  const existingTranslatedSourceContent = serializeInnerXml(
    existingTranslatedSource
  );
  const existingTranslatedContextGroup =
    existingTranslatedTransUnit.getElementsByTagName('context-group')[0];
  const serializedExistingTranslatedContextGroup = serializer.serializeToString(
    existingTranslatedContextGroup
  );
  const existingTranslatedTarget =
    existingTranslatedTransUnit.getElementsByTagName('target')[0];
  const existingTranslatedTargetContent = serializeInnerXml(
    existingTranslatedTarget
  );
  if (
    !existingTranslatedSource ||
    !existingTranslatedSourceContent ||
    !existingTranslatedContextGroup ||
    !serializedExistingTranslatedContextGroup ||
    !existingTranslatedTarget ||
    !existingTranslatedTargetContent
  ) {
    throw new Error(
      `No <source> or no <context-group> or no <target> found in existing unit with id ${existingTranslatedTransUnit.getAttribute(
        'id'
      )}`
    );
  }

  const existingTranslatedTargetProposal =
    existingTranslatedTransUnit.getElementsByTagName('target-proposal')[0];
  const existingTranslatedTargetProposalContent = serializeInnerXml(
    existingTranslatedTargetProposal
  );

  let mergeRequired = false;

  if (existingTranslatedSourceContent !== originalSourceContent) {
    mergeRequired = true;
    console.warn(
      `ℹ️ Source text changed for unit ${existingTranslatedTransUnit.getAttribute(
        'id'
      )}. Keeping existing target.`
    );
  }

  if (
    serializedExistingTranslatedContextGroup !== serializedOriginalContextGroup
  ) {
    mergeRequired = true;
  }

  // The 'target-proposal' has possibly changed.
  const latestTargetProposal = escapeBracesWithXliffPlaceholders(
    ERROR_MESSAGE_TRANSLATION_PROPOSALS[originalTransUnitId]
  );

  if (!mergeRequired) {
    if (latestTargetProposal !== existingTranslatedTargetProposalContent) {
      removeNodeFromUnit(existingTranslatedTransUnit, 'target-proposal');
      if (latestTargetProposal) {
        addNodeToUnit(
          translatedFile,
          existingTranslatedTransUnit,
          'target-proposal',
          latestTargetProposal,
          'source'
        );
      }
    }
    return existingTranslatedTransUnit;
  }

  // Merge is required

  addNodeToUnit(
    translatedFile,
    originalTransUnitClone,
    'target',
    existingTranslatedTargetContent,
    'source'
  );

  if (latestTargetProposal) {
    addNodeToUnit(
      translatedFile,
      originalTransUnitClone,
      'target-proposal',
      latestTargetProposal,
      'source'
    );
  }

  return originalTransUnitClone;
}

function mergeXlf(originalFile: XmlDocument, translatedFilePath: string): void {
  const translatedFileContent = fs.readFileSync(translatedFilePath, 'utf-8');
  const translatedFile: XmlDocument = parser.parseFromString(
    translatedFileContent,
    'application/xml'
  );

  const originalFileTransUnits: XmlElement[] = Array.from(
    originalFile.getElementsByTagName('trans-unit')
  );
  const translatedFileBody = translatedFile.getElementsByTagName('body')[0];
  if (!translatedFileBody)
    throw new Error('No <body> found in translated file');

  const translatedFileTransUnits: XmlElement[] = Array.from(
    translatedFileBody.getElementsByTagName('trans-unit')
  );
  const translatedFileTransUnitsById = new Map<string, XmlElement>();

  for (const transUnit of translatedFileTransUnits) {
    const id = transUnit.getAttribute('id');
    if (id) {
      translatedFileTransUnitsById.set(id, transUnit);
    }
  }

  // Clear existing body content
  while (translatedFileBody.firstChild) {
    translatedFileBody.removeChild(translatedFileBody.firstChild);
  }

  for (const originalTransUnit of originalFileTransUnits) {
    const originalTransUnitClone = originalTransUnit.cloneNode(true);
    if (!isElement(originalTransUnitClone)) {
      throw new Error(
        `Original trans-unit is not an element: ${originalTransUnit}`
      );
    }
    const originalTransUnitId = originalTransUnitClone.getAttribute('id');
    if (!originalTransUnitId) {
      throw new Error(
        `No 'id' attribute found in original trans-unit: ${originalTransUnit}`
      );
    }
    const originalTransUnitSource =
      originalTransUnitClone.getElementsByTagName('source')?.[0];
    if (!originalTransUnitSource) {
      throw new Error(
        `No <source> found in unit with id ${originalTransUnitId} in original file`
      );
    }

    const existingTranslatedTransUnit =
      translatedFileTransUnitsById.get(originalTransUnitId);
    let mergedTransUnit: XmlElement;

    if (existingTranslatedTransUnit) {
      mergedTransUnit = mergeExistingUnit(
        translatedFile,
        existingTranslatedTransUnit,
        originalTransUnitId,
        originalTransUnitClone
      );
    } else {
      mergedTransUnit = createNewUnit(
        translatedFilePath,
        originalTransUnitId,
        originalTransUnitClone,
        translatedFile
      );
    }

    translatedFileBody.appendChild(mergedTransUnit);
  }

  // Format with xml-formatter
  const formattedTargetFile = formatXml(translatedFile);

  fs.writeFileSync(translatedFilePath, formattedTargetFile, 'utf-8');
  console.log(`✅ Merged ${path.basename(translatedFilePath)}`);
}

function main() {
  const args = parseArgs();

  const sourcePath = `${args.i18nFolder}/messages.xlf`;
  if (!fs.existsSync(sourcePath)) {
    console.error(`❌ Source file missing: ${sourcePath}`);
    process.exit(1);
  }
  const sourceContent = fs.readFileSync(sourcePath, 'utf-8');
  const sourceDoc: XmlDocument = parser.parseFromString(
    sourceContent,
    'application/xml'
  );
  const formattedSourceFile = formatXml(sourceDoc);
  fs.writeFileSync(sourcePath, formattedSourceFile, 'utf-8');
  console.log(`✅ Formatted source file: ${path.basename(sourcePath)}`);
  const formattedSourceContent = fs.readFileSync(sourcePath, 'utf-8');
  const formattedSourceDoc: XmlDocument = parser.parseFromString(
    formattedSourceContent,
    'application/xml'
  );

  for (const lang of args.targetLangs) {
    const targetPath = `${args.i18nFolder}/messages.${lang}.xlf`;
    if (fs.existsSync(targetPath)) {
      mergeXlf(formattedSourceDoc, targetPath);
    } else {
      console.warn(`⚠️ Target file missing: ${targetPath}`);
    }
  }
}

main();
