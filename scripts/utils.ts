import { XMLSerializer, Document as XmlDocument } from '@xmldom/xmldom';
import format from 'xml-formatter';

const serializer = new XMLSerializer();

const FORMATTER_CONFIG = {
  indentation: '  ',
  collapseContent: true,
  lineSeparator: '\n',
  whiteSpaceAtEndOfSelfclosingTag: true,
};

export function formatXml(file: XmlDocument): string {
  return format(serializer.serializeToString(file), FORMATTER_CONFIG);
}
