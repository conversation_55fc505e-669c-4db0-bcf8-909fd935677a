import * as fs from 'fs';
import * as path from 'path';
import minimist from 'minimist';
import {
  DOMParser,
  Element as XmlElement,
  Document as XmlDocument,
} from '@xmldom/xmldom';
import { formatXml } from './utils';

const parser = new DOMParser();

/**
 * Parse command line arguments
 */
function parseArgs(): {
  i18nFolder: string;
  targetLangs: string[];
  overwriteTargets: boolean;
} {
  const argv = minimist(process.argv.slice(2), {
    string: ['i18n-folder', 'target-langs'],
    boolean: ['overwrite-targets'],
    default: {
      i18nFolder: '',
      targetLangs: ['de'],
      overwriteTargets: false,
    },
  });

  return {
    i18nFolder: argv['i18n-folder'],
    targetLangs: argv['target-langs']
      .split(',')
      .map((lang: string) => lang.trim()),
    overwriteTargets: argv['overwrite-targets'],
  };
}

function applyTranslationProposals(
  translationFile: XmlDocument,
  translationFilePath: string,
  overwriteTargets: boolean
): void {
  const transUnits: XmlElement[] = Array.from(
    translationFile.getElementsByTagName('trans-unit')
  );

  let changeCounter = 0;

  for (const transUnit of transUnits) {
    const targetProposal = transUnit.getElementsByTagName('target-proposal')[0];
    if (!targetProposal) {
      continue;
    }
    const target = transUnit.getElementsByTagName('target')[0];
    if (!target) {
      throw new Error(
        `No <target> found in unit with id ${transUnit.getAttribute('id')}.`
      );
    }
    const currentTargetText = target.textContent?.trim();

    const shouldReplace =
      overwriteTargets || currentTargetText?.startsWith('TODO: translate');

    if (shouldReplace) {
      // Clear target
      while (target.firstChild) {
        target.removeChild(target.firstChild);
      }

      // Import and append each child node from <target-proposal>
      for (let i = 0; i < targetProposal.childNodes.length; i++) {
        const child = targetProposal.childNodes[i];
        const imported = translationFile.importNode(child, true);
        target.appendChild(imported);
      }

      changeCounter++;
    }
  }

  // Format with xml-formatter
  const formattedTranslationFile = formatXml(translationFile);

  fs.writeFileSync(translationFilePath, formattedTranslationFile, 'utf-8');
  console.log(
    `✅ Applied ${changeCounter} translation proposals in ${path.basename(
      translationFilePath
    )}`
  );
}

function main() {
  const args = parseArgs();

  for (const lang of args.targetLangs) {
    const translationFilePath = `${args.i18nFolder}/messages.${lang}.xlf`;
    if (fs.existsSync(translationFilePath)) {
      const translationFileContent = fs.readFileSync(
        translationFilePath,
        'utf-8'
      );
      const translationFile: XmlDocument = parser.parseFromString(
        translationFileContent,
        'application/xml'
      );
      applyTranslationProposals(
        translationFile,
        translationFilePath,
        args.overwriteTargets
      );
    } else {
      console.warn(`⚠️ Target file missing: ${translationFilePath}`);
    }
  }
}

main();
