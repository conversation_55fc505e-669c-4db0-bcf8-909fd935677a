import { ChangeDetectionStrategy, Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

import { ButtonModule } from 'primeng/button';
import { StepsModule } from 'primeng/steps';
import { ScrollTopModule } from 'primeng/scrolltop';

import { pacs008FormSteps } from './pacs008.types';
import { FormStepperComponent } from '@shared/ui';

@Component({
  selector: 'app-pacs008',
  imports: [
    ReactiveFormsModule,
    FormStepperComponent,
    ButtonModule,
    StepsModule,
    ScrollTopModule,
  ],
  templateUrl: './pacs008.component.html',
  styleUrl: './pacs008.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Pacs008Component {
  pacs008FormSteps = pacs008FormSteps;
}
