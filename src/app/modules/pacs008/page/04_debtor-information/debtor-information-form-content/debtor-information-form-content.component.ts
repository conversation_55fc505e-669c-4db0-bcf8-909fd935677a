import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';
import {
  FormPageContentComponent,
  HiddenSectionComponent,
  OneOfSelectorComponent,
} from '@shared/ui';
import { FORM_METADATA } from '../../../../../generated/pacs008-form-metadata';
import {
  AnyBICDec2014IdentifierInputComponent,
  CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
  PostalAddress24__1InputComponent,
  LEIIdentifierInputComponent,
  CBPR_RestrictedFINXMax35TextInputComponent,
  ExternalOrganisationIdentification1CodeInputComponent,
  DateAndPlaceOfBirth1__1InputComponent,
  ExternalPersonIdentification1CodeInputComponent,
  CountryCodeInputComponent,
  CashAccount38__1InputComponent,
  PartyIdentification135__1InputComponent,
} from '../../../components';

@Component({
  selector: 'app-debtor-information-form-content',
  imports: [
    CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
    PostalAddress24__1InputComponent,
    AnyBICDec2014IdentifierInputComponent,
    LEIIdentifierInputComponent,
    CBPR_RestrictedFINXMax35TextInputComponent,
    HiddenSectionComponent,
    ExternalOrganisationIdentification1CodeInputComponent,
    OneOfSelectorComponent,
    DateAndPlaceOfBirth1__1InputComponent,
    ExternalPersonIdentification1CodeInputComponent,
    CountryCodeInputComponent,
    CashAccount38__1InputComponent,
    PartyIdentification135__1InputComponent,
  ],
  templateUrl: './debtor-information-form-content.component.html',
  styleUrl: './debtor-information-form-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebtorInformationFormContentComponent extends FormPageContentComponent {
  override pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();

  override labels = computed(() => FORM_METADATA[this.pageKey()].labels);
}
