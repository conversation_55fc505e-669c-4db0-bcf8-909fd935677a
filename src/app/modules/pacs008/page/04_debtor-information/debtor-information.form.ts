import { FormControl, NonNullableFormBuilder } from '@angular/forms';
import { FormValues } from '@shared/types';

export function getFormSchema(fb: NonNullableFormBuilder) {
  return {
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Nm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Dept': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-SubDept': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-StrtNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNb': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Flr': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstBx': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Room': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstCd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Ctry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-AdrLine': fb.array<
      FormControl<string>
    >([]),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-CtryOfRes': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-LEI': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Ccy': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Nm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Nm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Room': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine': fb.array<
      FormControl<string>
    >([]),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-CtryOfRes': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-AnyBIC': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-LEI': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Cd':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Cd':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr':
      fb.control(''),
  };
}

export type DebtorInformationFormSchema = ReturnType<typeof getFormSchema>;

export type DebtorInformationFormValues =
  FormValues<DebtorInformationFormSchema>;

export function getFormGroup(fb: NonNullableFormBuilder) {
  const schema = getFormSchema(fb);
  return fb.group(schema);
}
