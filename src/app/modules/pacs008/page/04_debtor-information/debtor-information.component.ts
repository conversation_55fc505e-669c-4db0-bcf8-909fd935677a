import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  FormPageComponent,
  InfoCardComponent,
  FormNavigationButtonsComponent,
} from '@shared/ui';
import { pacs008FormSteps } from '../pacs008.types';
import { FormRulesDirective } from '@helaba/iso20022-lib/directives';
import {
  FORM_METADATA,
  getErrorMessages,
} from '../../../../generated/pacs008-form-metadata';
import {
  DebtorInformationFormSchema,
  DebtorInformationFormValues,
  getFormGroup,
} from './debtor-information.form';
import { pacs008AffectedFields } from '@helaba/iso20022-lib/rules';
import { DebtorInformationFormContentComponent } from './debtor-information-form-content';

@Component({
  selector: 'app-debtor-information',
  imports: [
    ReactiveFormsModule,
    FormRulesDirective,
    DebtorInformationFormContentComponent,
    InfoCardComponent,
    FormNavigationButtonsComponent,
  ],
  templateUrl: './debtor-information.component.html',
  styleUrl: './debtor-information.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DebtorInformationComponent extends FormPageComponent<
  DebtorInformationFormSchema,
  DebtorInformationFormValues
> {
  override pageKey = 'debtorInformation';
  override formGroup = getFormGroup(this.fb);
  override formSteps = pacs008FormSteps;
  override affectedFields = pacs008AffectedFields;
  override formFields = FORM_METADATA[this.pageKey]
    .formFields as (keyof DebtorInformationFormSchema)[];
  override validationRules = FORM_METADATA[this.pageKey].validationRules;

  override initializeErrorMessages(): void {
    const errorMessages = getErrorMessages(this.pageKey);
    this.formErrorsService.setErrorMessages(errorMessages);
  }
}
