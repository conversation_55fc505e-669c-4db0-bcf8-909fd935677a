import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';
import {
  Priority3Code,
  priority3Codes,
  SettlementMethod1Code,
  settlementMethod1Codes,
} from '@shared/types';
import {
  FormPageContentComponent,
  FormRadioButtonGroupComponent,
  HiddenSectionComponent,
  SelectOption,
} from '@shared/ui';
import {
  BranchAndFinancialInstitutionIdentification6__1InputComponent,
  CashAccount38__1InputComponent,
  CBPR_DateTimeInputComponent,
  CBPR_TimeInputComponent,
  ISODateInputComponent,
} from '../../../components';
import { FORM_METADATA } from '../../../../../generated/pacs008-form-metadata';

@Component({
  selector: 'app-settlement-information-form-content',
  imports: [
    FormRadioButtonGroupComponent,
    ISODateInputComponent,
    HiddenSectionComponent,
    CBPR_DateTimeInputComponent,
    CBPR_TimeInputComponent,
    BranchAndFinancialInstitutionIdentification6__1InputComponent,
    CashAccount38__1InputComponent,
  ],
  templateUrl: './settlement-information-form-content.component.html',
  styleUrl: './settlement-information-form-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettlementInformationFormContentComponent extends FormPageContentComponent {
  override pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();

  override labels = computed(() => FORM_METADATA[this.pageKey()].labels);

  // Options for select fields
  settlementMethodOptions: SelectOption<SettlementMethod1Code>[] =
    settlementMethod1Codes.map((code) => ({
      key: code,
      label: code,
      value: code,
    }));
  settlementPriorityOptions: SelectOption<Priority3Code>[] = priority3Codes.map(
    (code) => ({
      key: code,
      label: code,
      value: code,
    })
  );
}
