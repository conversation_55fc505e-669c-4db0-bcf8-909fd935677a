<app-form-radio-button-group
  [label]="labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd']"
  [fieldNames]="['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd']"
  [options]="settlementMethodOptions"
  [isReadOnly]="isReadOnly()"
/>
<app-iso-date-input
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmDt']"
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmDt"
  [isReadOnly]="isReadOnly()"
/>
<app-form-radio-button-group
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmPrty']"
  [fieldNames]="['FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmPrty']"
  [options]="settlementPriorityOptions"
  [isReadOnly]="isReadOnly()"
/>
<app-hidden-section
  header="Settlement Time Indication"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn']"
>
  <app-cbpr-date-time-input
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm']"
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm"
    [isReadOnly]="isReadOnly()"
  />
  <app-cbpr-date-time-input
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm']"
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm"
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-hidden-section
  header="Settlement Time Request"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq']"
>
  <app-cbpr-time-input
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm']"
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm"
    [isReadOnly]="isReadOnly()"
  />
  <app-cbpr-time-input
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm']"
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm"
    [isReadOnly]="isReadOnly()"
  />
  <app-cbpr-time-input
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm']"
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm"
    [isReadOnly]="isReadOnly()"
  />
  <app-cbpr-time-input
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm']"
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm"
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>

<app-hidden-section
  header="Instructing Reimbursement Agent"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt']"
>
  <app-branch-and-financial-institution-identification-6-1-input
    bicfiFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI"
    [bicfiLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI'
      ]
    "
    clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
    [clearingSystemIdCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
      ]
    "
    clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"
    [clearingSystemMemberIdLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId'
      ]
    "
    leiIdentifierFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI"
    [leiIdentifierLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI'
      ]
    "
    nameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"
    [nameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm'
      ]
    "
    departmentFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"
    [departmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept'
      ]
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"
    [subDepartmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept'
      ]
    "
    streetNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"
    [streetNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm'
      ]
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"
    [buildingNumberLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb'
      ]
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"
    [buildingNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm'
      ]
    "
    floorFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"
    [floorLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr'
      ]
    "
    postBoxFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"
    [postBoxLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx'
      ]
    "
    roomFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"
    [roomLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room'
      ]
    "
    postCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"
    [postCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd'
      ]
    "
    townNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"
    [townNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm'
      ]
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm'
      ]
    "
    districtNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"
    [districtNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm'
      ]
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn'
      ]
    "
    countryFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"
    [countryLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry'
      ]
    "
    addressLineFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"
    [addressLineLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine'
      ]
    "
    [isReadOnly]="isReadOnly()"
  />
  <app-hidden-section
    header="Instructing Reimbursement Agent Account"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct']"
  >
    <app-cash-account-38-1-input
      ibanFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN"
      [ibanLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN'
        ]
      "
      idFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id"
      [idLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id'
        ]
      "
      identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd"
      [identificationSchemeNameCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd'
        ]
      "
      proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry"
      [proprietaryIdentificationSchemeNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry'
        ]
      "
      identificationIssuerFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr"
      [identificationIssuerLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr'
        ]
      "
      typeCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd"
      [typeCodeLabel]="
        labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd']
      "
      proprietaryTypeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry"
      [proprietaryTypeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry'
        ]
      "
      currencyFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Ccy"
      [currencyLabel]="
        labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Ccy']
      "
      nameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Nm"
      [nameLabel]="
        labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Nm']
      "
      proxyTypeCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd"
      [proxyTypeCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd'
        ]
      "
      proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry"
      [proprietaryProxyTypeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry'
        ]
      "
      proxyIdFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id"
      [proxyIdLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
</app-hidden-section>

<app-hidden-section
  header="Instructed Reimbursement Agent"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt']"
>
  <app-branch-and-financial-institution-identification-6-1-input
    bicfiFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI"
    [bicfiLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI'
      ]
    "
    clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
    [clearingSystemIdCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
      ]
    "
    clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"
    [clearingSystemMemberIdLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId'
      ]
    "
    leiIdentifierFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI"
    [leiIdentifierLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI'
      ]
    "
    nameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"
    [nameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm'
      ]
    "
    departmentFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"
    [departmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept'
      ]
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"
    [subDepartmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept'
      ]
    "
    streetNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"
    [streetNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm'
      ]
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"
    [buildingNumberLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb'
      ]
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"
    [buildingNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm'
      ]
    "
    floorFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"
    [floorLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr'
      ]
    "
    postBoxFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"
    [postBoxLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx'
      ]
    "
    roomFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"
    [roomLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room'
      ]
    "
    postCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"
    [postCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd'
      ]
    "
    townNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"
    [townNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm'
      ]
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm'
      ]
    "
    districtNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"
    [districtNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm'
      ]
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn'
      ]
    "
    countryFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"
    [countryLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry'
      ]
    "
    addressLineFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"
    [addressLineLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine'
      ]
    "
    [isReadOnly]="isReadOnly()"
  />
  <app-hidden-section
    header="Instructed Reimbursement Agent Account"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct']"
  >
    <app-cash-account-38-1-input
      ibanFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN"
      [ibanLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN'
        ]
      "
      idFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id"
      [idLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id'
        ]
      "
      identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd"
      [identificationSchemeNameCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd'
        ]
      "
      proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry"
      [proprietaryIdentificationSchemeNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry'
        ]
      "
      identificationIssuerFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr"
      [identificationIssuerLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr'
        ]
      "
      typeCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd"
      [typeCodeLabel]="
        labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd']
      "
      proprietaryTypeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry"
      [proprietaryTypeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry'
        ]
      "
      currencyFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Ccy"
      [currencyLabel]="
        labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Ccy']
      "
      nameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Nm"
      [nameLabel]="
        labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Nm']
      "
      proxyTypeCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd"
      [proxyTypeCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd'
        ]
      "
      proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry"
      [proprietaryProxyTypeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry'
        ]
      "
      proxyIdFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id"
      [proxyIdLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
</app-hidden-section>

<app-hidden-section
  header="Third Reimbursement Agent"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt']"
>
  <app-branch-and-financial-institution-identification-6-1-input
    bicfiFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI"
    [bicfiLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI'
      ]
    "
    clearingSystemIdCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
    [clearingSystemIdCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd'
      ]
    "
    clearingSystemMemberIdFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"
    [clearingSystemMemberIdLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId'
      ]
    "
    leiIdentifierFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI"
    [leiIdentifierLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI'
      ]
    "
    nameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"
    [nameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm'
      ]
    "
    departmentFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"
    [departmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept'
      ]
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"
    [subDepartmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept'
      ]
    "
    streetNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"
    [streetNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm'
      ]
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"
    [buildingNumberLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb'
      ]
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"
    [buildingNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm'
      ]
    "
    floorFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"
    [floorLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr'
      ]
    "
    postBoxFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"
    [postBoxLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx'
      ]
    "
    roomFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"
    [roomLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room'
      ]
    "
    postCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"
    [postCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd'
      ]
    "
    townNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"
    [townNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm'
      ]
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm'
      ]
    "
    districtNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"
    [districtNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm'
      ]
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn'
      ]
    "
    countryFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"
    [countryLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry'
      ]
    "
    addressLineFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"
    [addressLineLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine'
      ]
    "
    [isReadOnly]="isReadOnly()"
  />
  <app-hidden-section
    header="Third Reimbursement Agent Account"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct']"
  >
    <app-cash-account-38-1-input
      ibanFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN"
      [ibanLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN'
        ]
      "
      idFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id"
      [idLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id'
        ]
      "
      identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd"
      [identificationSchemeNameCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd'
        ]
      "
      proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry"
      [proprietaryIdentificationSchemeNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry'
        ]
      "
      identificationIssuerFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr"
      [identificationIssuerLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr'
        ]
      "
      typeCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd"
      [typeCodeLabel]="
        labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd']
      "
      proprietaryTypeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry"
      [proprietaryTypeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry'
        ]
      "
      currencyFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy"
      [currencyLabel]="
        labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy']
      "
      nameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Nm"
      [nameLabel]="
        labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Nm']
      "
      proxyTypeCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd"
      [proxyTypeCodeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd'
        ]
      "
      proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry"
      [proprietaryProxyTypeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry'
        ]
      "
      proxyIdFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id"
      [proxyIdLabel]="
        labels()[
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
</app-hidden-section>

<app-hidden-section
  header="Settlement Account"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct']"
>
  <app-cash-account-38-1-input
    ibanFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN"
    [ibanLabel]="
      labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN']
    "
    idFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id"
    [idLabel]="
      labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id']
    "
    identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd"
    [identificationSchemeNameCodeLabel]="
      labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd']
    "
    proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry"
    [proprietaryIdentificationSchemeNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry'
      ]
    "
    identificationIssuerFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr"
    [identificationIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr']
    "
    typeCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd"
    [typeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd']
    "
    proprietaryTypeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry"
    [proprietaryTypeLabel]="
      labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry']
    "
    currencyFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy"
    [currencyLabel]="
      labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy']
    "
    nameFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm"
    [nameLabel]="labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm']"
    proxyTypeCodeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd"
    [proxyTypeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd']
    "
    proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry"
    [proprietaryProxyTypeLabel]="
      labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry']
    "
    proxyIdFieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id"
    [proxyIdLabel]="
      labels()['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id']
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
