import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  FormPageComponent,
  InfoCardComponent,
  FormNavigationButtonsComponent,
} from '@shared/ui';
import { pacs008FormSteps } from '../pacs008.types';
import { FormRulesDirective } from '@helaba/iso20022-lib/directives';
import {
  FORM_METADATA,
  getErrorMessages,
} from '../../../../generated/pacs008-form-metadata';
import {
  getFormGroup,
  SettlementInformationFormSchema,
  SettlementInformationFormValues,
} from './settlement-information.form';
import { SettlementInformationFormContentComponent } from './settlement-information-form-content';
import { pacs008AffectedFields } from '@helaba/iso20022-lib/rules';

@Component({
  selector: 'app-settlement-information',
  imports: [
    ReactiveFormsModule,
    FormRulesDirective,
    SettlementInformationFormContentComponent,
    InfoCardComponent,
    FormNavigationButtonsComponent,
  ],
  templateUrl: './settlement-information.component.html',
  styleUrl: './settlement-information.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettlementInformationComponent extends FormPageComponent<
  SettlementInformationFormSchema,
  SettlementInformationFormValues
> {
  override pageKey = 'settlementInformation';
  override formGroup = getFormGroup(this.fb);
  override formSteps = pacs008FormSteps;
  override affectedFields = pacs008AffectedFields;
  override formFields = FORM_METADATA[this.pageKey]
    .formFields as (keyof SettlementInformationFormSchema)[];
  override validationRules = FORM_METADATA[this.pageKey].validationRules;

  override initializeErrorMessages(): void {
    const errorMessages = getErrorMessages(this.pageKey);
    this.formErrorsService.setErrorMessages(errorMessages);
  }
}
