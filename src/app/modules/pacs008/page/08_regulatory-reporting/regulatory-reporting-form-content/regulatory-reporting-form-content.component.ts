import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';
import {
  FormPageContentComponent,
  HiddenSectionComponent,
  FormRadioButtonGroupComponent,
  SelectOption,
  FormTextInputComponent,
} from '@shared/ui';
import { FORM_METADATA } from '../../../../../generated/pacs008-form-metadata';
import {
  CBPR_RestrictedFINXMax35TextInputComponent,
  CountryCodeInputComponent,
  CBPR_RestrictedFINXMax140TextInputComponent,
  ISODateInputComponent,
  CBPR_Amount__1InputComponent,
} from '../../../components';
import {
  RegulatoryReportingType1Code,
  regulatoryReportingType1Codes,
} from '@shared/types';

@Component({
  selector: 'app-regulatory-reporting-form-content',
  imports: [
    CBPR_RestrictedFINXMax35TextInputComponent,
    HiddenSectionComponent,
    CountryCodeInputComponent,
    FormRadioButtonGroupComponent,
    CBPR_RestrictedFINXMax140TextInputComponent,
    ISODateInputComponent,
    FormTextInputComponent,
    CBPR_Amount__1InputComponent,
  ],
  templateUrl: './regulatory-reporting-form-content.component.html',
  styleUrl: './regulatory-reporting-form-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegulatoryReportingFormContentComponent extends FormPageContentComponent {
  override pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();

  override labels = computed(() => FORM_METADATA[this.pageKey()].labels);

  regulatoryReportingTypeCodeOptions: SelectOption<RegulatoryReportingType1Code>[] =
    regulatoryReportingType1Codes.map((code) => ({
      key: code,
      label: code,
      value: code,
    }));
}
