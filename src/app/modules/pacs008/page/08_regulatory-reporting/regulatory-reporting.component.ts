import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  FormPageComponent,
  InfoCardComponent,
  FormNavigationButtonsComponent,
} from '@shared/ui';
import { pacs008FormSteps } from '../pacs008.types';
import { FormRulesDirective } from '@helaba/iso20022-lib/directives';
import {
  FORM_METADATA,
  getErrorMessages,
} from '../../../../generated/pacs008-form-metadata';
import {
  RegulatoryReportingFormSchema,
  RegulatoryReportingFormValues,
  getFormGroup,
} from './regulatory-reporting.form';
import { pacs008AffectedFields } from '@helaba/iso20022-lib/rules';
import { RegulatoryReportingFormContentComponent } from './regulatory-reporting-form-content';

@Component({
  selector: 'app-regulatory-reporting',
  imports: [
    ReactiveFormsModule,
    FormRulesDirective,
    RegulatoryReportingFormContentComponent,
    InfoCardComponent,
    FormNavigationButtonsComponent,
  ],
  templateUrl: './regulatory-reporting.component.html',
  styleUrl: './regulatory-reporting.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegulatoryReportingComponent extends FormPageComponent<
  RegulatoryReportingFormSchema,
  RegulatoryReportingFormValues
> {
  override pageKey = 'regulatoryReporting';
  override formGroup = getFormGroup(this.fb);
  override formSteps = pacs008FormSteps;
  override affectedFields = pacs008AffectedFields;
  override formFields = FORM_METADATA[this.pageKey]
    .formFields as (keyof RegulatoryReportingFormSchema)[];
  override validationRules = FORM_METADATA[this.pageKey].validationRules;

  override initializeErrorMessages(): void {
    const errorMessages = getErrorMessages(this.pageKey);
    this.formErrorsService.setErrorMessages(errorMessages);
  }
}
