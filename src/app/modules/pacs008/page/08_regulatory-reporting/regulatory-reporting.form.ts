import { NonNullableFormBuilder } from '@angular/forms';
import { FormValues } from '@shared/types';

export function getFormSchema(fb: NonNullableFormBuilder) {
  return {
    'FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Nm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Ctry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Tp': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Dt': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Ctry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Cd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Inf': fb.control(''),
  };
}

export type RegulatoryReportingFormSchema = ReturnType<typeof getFormSchema>;

export type RegulatoryReportingFormValues =
  FormValues<RegulatoryReportingFormSchema>;

export function getFormGroup(fb: NonNullableFormBuilder) {
  const schema = getFormSchema(fb);
  return fb.group(schema);
}
