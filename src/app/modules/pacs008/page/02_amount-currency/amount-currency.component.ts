import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  FormPageComponent,
  InfoCardComponent,
  FormNavigationButtonsComponent,
} from '@shared/ui';
import { pacs008FormSteps } from '../pacs008.types';
import { FormRulesDirective } from '@helaba/iso20022-lib/directives';
import {
  FORM_METADATA,
  getErrorMessages,
} from '../../../../generated/pacs008-form-metadata';
import {
  AmountCurrencyFormSchema,
  AmountCurrencyFormValues,
  getFormGroup,
} from './amount-currency.form';
import { AmountCurrencyFormContentComponent } from './amount-currency-form-content';
import { pacs008AffectedFields } from '@helaba/iso20022-lib/rules';

@Component({
  selector: 'app-amount-currency',
  imports: [
    ReactiveFormsModule,
    FormRulesDirective,
    AmountCurrencyFormContentComponent,
    InfoCardComponent,
    FormNavigationButtonsComponent,
  ],
  templateUrl: './amount-currency.component.html',
  styleUrl: './amount-currency.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AmountCurrencyComponent extends FormPageComponent<
  AmountCurrencyFormSchema,
  AmountCurrencyFormValues
> {
  override pageKey = 'amountCurrency';
  override formGroup = getFormGroup(this.fb);
  override formSteps = pacs008FormSteps;
  override affectedFields = pacs008AffectedFields;
  override formFields = FORM_METADATA[this.pageKey]
    .formFields as (keyof AmountCurrencyFormSchema)[];
  override validationRules = FORM_METADATA[this.pageKey].validationRules;

  override initializeErrorMessages(): void {
    const errorMessages = getErrorMessages(this.pageKey);
    this.formErrorsService.setErrorMessages(errorMessages);
  }
}
