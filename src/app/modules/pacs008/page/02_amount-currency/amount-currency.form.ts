import { FormControl, NonNullableFormBuilder } from '@angular/forms';
import { FormValues } from '@shared/types';

export function getFormSchema(fb: NonNullableFormBuilder) {
  return {
    'FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-amount': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-Ccy': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-XchgRate': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgBr': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-BICFI':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-LEI': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine':
      fb.array<FormControl<string>>([]),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-amount': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy': fb.control(''),
  };
}

export type AmountCurrencyFormSchema = ReturnType<typeof getFormSchema>;

export type AmountCurrencyFormValues = FormValues<AmountCurrencyFormSchema>;

export function getFormGroup(fb: NonNullableFormBuilder) {
  const schema = getFormSchema(fb);
  return fb.group(schema);
}
