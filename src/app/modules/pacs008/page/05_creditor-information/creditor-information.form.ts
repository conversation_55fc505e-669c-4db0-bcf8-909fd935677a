import { FormControl, NonNullableFormBuilder } from '@angular/forms';
import { FormValues } from '@shared/types';

export function getFormSchema(fb: NonNullableFormBuilder) {
  return {
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Nm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Dept': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-SubDept': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-StrtNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNb': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Flr': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstBx': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Room': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstCd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Ctry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-AdrLine': fb.array<
      FormControl<string>
    >([]),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-CtryOfRes': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-AnyBIC': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-LEI': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Cd':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Cd':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr': fb.control(''),

    'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Ccy': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Nm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Nm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Room': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine': fb.array<
      FormControl<string>
    >([]),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-AnyBIC': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-LEI': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Cd':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Cd':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr':
      fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-CtryOfRes': fb.control(''),
  };
}

export type CreditorInformationFormSchema = ReturnType<typeof getFormSchema>;

export type CreditorInformationFormValues =
  FormValues<CreditorInformationFormSchema>;

export function getFormGroup(fb: NonNullableFormBuilder) {
  const schema = getFormSchema(fb);
  return fb.group(schema);
}
