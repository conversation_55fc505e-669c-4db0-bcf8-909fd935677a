import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  FormPageComponent,
  InfoCardComponent,
  FormNavigationButtonsComponent,
} from '@shared/ui';
import { pacs008FormSteps } from '../pacs008.types';
import { FormRulesDirective } from '@helaba/iso20022-lib/directives';
import {
  FORM_METADATA,
  getErrorMessages,
} from '../../../../generated/pacs008-form-metadata';
import {
  CreditorInformationFormSchema,
  CreditorInformationFormValues,
  getFormGroup,
} from './creditor-information.form';
import { pacs008AffectedFields } from '@helaba/iso20022-lib/rules';
import { CreditorInformationFormContentComponent } from './creditor-information-form-content';

@Component({
  selector: 'app-creditor-information',
  imports: [
    ReactiveFormsModule,
    FormRulesDirective,
    CreditorInformationFormContentComponent,
    InfoCardComponent,
    FormNavigationButtonsComponent,
  ],
  templateUrl: './creditor-information.component.html',
  styleUrl: './creditor-information.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreditorInformationComponent extends FormPageComponent<
  CreditorInformationFormSchema,
  CreditorInformationFormValues
> {
  override pageKey = 'creditorInformation';
  override formGroup = getFormGroup(this.fb);
  override formSteps = pacs008FormSteps;
  override affectedFields = pacs008AffectedFields;
  override formFields = FORM_METADATA[this.pageKey]
    .formFields as (keyof CreditorInformationFormSchema)[];
  override validationRules = FORM_METADATA[this.pageKey].validationRules;

  override initializeErrorMessages(): void {
    const errorMessages = getErrorMessages(this.pageKey);
    this.formErrorsService.setErrorMessages(errorMessages);
  }
}
