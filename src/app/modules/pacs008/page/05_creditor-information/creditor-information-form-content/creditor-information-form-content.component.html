<app-cbpr-restricted-finx-max-140-text-extended-input
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Nm"
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Nm']"
  [isReadOnly]="isReadOnly()"
/>
<app-postal-address-24-1-input
  departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Dept"
  [departmentLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Dept']
  "
  subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-SubDept"
  [subDepartmentLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-SubDept']
  "
  streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-StrtNm"
  [streetNameLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-StrtNm']
  "
  buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNb"
  [buildingNumberLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNb']
  "
  buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNm"
  [buildingNameLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNm']
  "
  floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Flr"
  [floorLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Flr']"
  postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstBx"
  [postBoxLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstBx']"
  roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Room"
  [roomLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Room']"
  postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstCd"
  [postCodeLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstCd']"
  townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnNm"
  [townNameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnNm']"
  townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm"
  [townLocationNameLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm']
  "
  districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm"
  [districtNameLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm']
  "
  countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn"
  [countrySubdivisionLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn']
  "
  countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Ctry"
  [countryLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Ctry']"
  addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-AdrLine"
  [addressLineLabel]="
    labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-AdrLine']
  "
  [isReadOnly]="isReadOnly()"
/>
<app-country-code-input
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-CtryOfRes"
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-CtryOfRes']"
  [isReadOnly]="isReadOnly()"
/>
<app-hidden-section
  header="Creditor Information"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id']"
>
  <app-party-38-choice-1-input
    bicFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-AnyBIC"
    [bicLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-AnyBIC']"
    leiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-LEI"
    [leiLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-LEI']"
    organisationIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id"
    [organisationIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id']
    "
    organisationIdSchemeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Cd"
    [organisationIdSchemeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Cd']
    "
    proprietaryOrganisationIdSchemeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry"
    [proprietaryOrganisationIdSchemeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry']
    "
    organisationIdIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr"
    [organisationIdIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr']
    "
    birthDateFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt"
    [birthDateLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt'
      ]
    "
    provinceOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth"
    [provinceOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth'
      ]
    "
    cityOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"
    [cityOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth'
      ]
    "
    countryOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth"
    [countryOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth'
      ]
    "
    individualIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id"
    [individualIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id']
    "
    individualIdSchemeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Cd"
    [individualIdSchemeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Cd']
    "
    proprietaryIndividualIdSchemeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry"
    [proprietaryIndividualIdSchemeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry'
      ]
    "
    individualIdIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr"
    [individualIdIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr']
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-hidden-section
  header="Debtor Account"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct']"
>
  <app-cash-account-38-1-input
    ibanFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN"
    [ibanLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN']"
    idFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id"
    [idLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id']"
    identificationSchemeNameCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd"
    [identificationSchemeNameCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd']
    "
    proprietaryIdentificationSchemeNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry"
    [proprietaryIdentificationSchemeNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry']
    "
    identificationIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr"
    [identificationIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr']
    "
    typeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd"
    [typeCodeLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd']"
    proprietaryTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry"
    [proprietaryTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry']
    "
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Ccy"
    [currencyLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Ccy']"
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Nm"
    [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Nm']"
    proxyTypeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd"
    [proxyTypeCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd']
    "
    proprietaryProxyTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry"
    [proprietaryProxyTypeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry']
    "
    proxyIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id"
    [proxyIdLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id']"
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-hidden-section
  header="Ultimate Creditor"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr']"
>
  <app-party-identification-135-1-input
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Nm"
    [nameLabel]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Nm']"
    departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept"
    [departmentLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept']
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept"
    [subDepartmentLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept']
    "
    streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm"
    [streetNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm']
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb"
    [buildingNumberLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb']
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm"
    [buildingNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm']
    "
    floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr"
    [floorLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr']
    "
    postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx"
    [postBoxLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx']
    "
    roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Room"
    [roomLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Room']
    "
    postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd"
    [postCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd']
    "
    townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm"
    [townNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm']
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm']
    "
    districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm"
    [districtNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm']
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn']
    "
    countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry"
    [countryLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry']
    "
    addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine"
    [addressLineLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine']
    "
    bicFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-AnyBIC"
    [bicLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-AnyBIC']
    "
    leiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-LEI"
    [leiLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-LEI']
    "
    organisationIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id"
    [organisationIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id']
    "
    organisationIdSchemeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Cd"
    [organisationIdSchemeCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Cd'
      ]
    "
    proprietaryOrganisationIdSchemeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry"
    [proprietaryOrganisationIdSchemeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry'
      ]
    "
    organisationIdIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr"
    [organisationIdIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr']
    "
    birthDateFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt"
    [birthDateLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt'
      ]
    "
    provinceOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth"
    [provinceOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth'
      ]
    "
    cityOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"
    [cityOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth'
      ]
    "
    countryOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth"
    [countryOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth'
      ]
    "
    individualIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id"
    [individualIdLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id']
    "
    individualIdSchemeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Cd"
    [individualIdSchemeCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Cd'
      ]
    "
    proprietaryIndividualIdSchemeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry"
    [proprietaryIndividualIdSchemeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry'
      ]
    "
    individualIdIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr"
    [individualIdIssuerLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr']
    "
    countryOfResidenceFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-CtryOfRes"
    [countryOfResidenceLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-CtryOfRes']
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
