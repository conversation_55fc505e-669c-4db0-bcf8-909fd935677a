import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';
import { FormPageContentComponent, HiddenSectionComponent } from '@shared/ui';
import { FORM_METADATA } from '../../../../../generated/pacs008-form-metadata';
import {
  CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
  PostalAddress24__1InputComponent,
  CountryCodeInputComponent,
  CashAccount38__1InputComponent,
  PartyIdentification135__1InputComponent,
  Party38Choice__1InputComponent,
} from '../../../components';

@Component({
  selector: 'app-creditor-information-form-content',
  imports: [
    CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
    PostalAddress24__1InputComponent,
    HiddenSectionComponent,
    CountryCodeInputComponent,
    CashAccount38__1InputComponent,
    PartyIdentification135__1InputComponent,
    Party38Choice__1InputComponent,
  ],
  templateUrl: './creditor-information-form-content.component.html',
  styleUrl: './creditor-information-form-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreditorInformationFormContentComponent extends FormPageContentComponent {
  override pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();

  override labels = computed(() => FORM_METADATA[this.pageKey()].labels);
}
