import { FormStep } from '@shared/ui';
import { CompletionPageKey } from '@shared/types';
import { PaymentBasicsComponent } from './01_payment-basics';
import { AmountCurrencyComponent } from './02_amount-currency';
import { SettlementInformationComponent } from './03_settlement-information';
import { DebtorInformationComponent } from './04_debtor-information';
import { CreditorInformationComponent } from './05_creditor-information';
import { FinancialInstitutionInformationComponent } from './06_financial-institution-information';
import { RemittanceInformationComponent } from './07_remittance-information';
import { RegulatoryReportingComponent } from './08_regulatory-reporting';
import { Pacs008ReviewSubmitComponent } from './09_review-submit';

// TODO: Make sure FormStep type 'any' generic is good.
export const pacs008FormSteps: FormStep<any>[] = [
  {
    key: 'paymentBasics',
    item: {
      label: $localize`Payment Basics`,
    },
    component: PaymentBasicsComponent,
  },
  {
    key: 'amountCurrency',
    item: {
      label: $localize`Amount & Currency`,
    },
    component: AmountCurrencyComponent,
  },
  {
    key: 'settlementInformation',
    item: {
      label: $localize`Information about payment settlement`, //Informationen zur Abwicklung der Zahlung (Settlement)
    },
    component: SettlementInformationComponent,
  },
  {
    key: 'debtorInformation',
    item: {
      label: $localize`Information about debtor`,
    },
    component: DebtorInformationComponent,
  },
  {
    key: 'creditorInformation',
    item: {
      label: $localize`Information about creditor`,
    },
    component: CreditorInformationComponent,
  },
  {
    key: 'financialInstitutionInformation',
    item: {
      label: $localize`Information about the financial institutions involved`,
    },
    component: FinancialInstitutionInformationComponent,
  },
  {
    key: 'remittanceInformation',
    item: {
      label: $localize`Remittance Information`,
    },
    component: RemittanceInformationComponent,
  },
  {
    key: 'regulatoryReporting',
    item: {
      label: $localize`Regulatory Reporting`,
    },
    component: RegulatoryReportingComponent,
  },
  {
    key: CompletionPageKey.COMPLETION,
    item: {
      label: $localize`Review & Submit`,
    },
    component: Pacs008ReviewSubmitComponent,
  },
];

export const pacs008FormPageTitles = pacs008FormSteps.reduce<
  Record<string, string>
>((acc, step) => {
  const title = step.item.label;
  if (title) {
    acc[step.key] = title;
  }
  return acc;
}, {});
