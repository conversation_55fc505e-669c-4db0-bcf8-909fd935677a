import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  FormPageComponent,
  InfoCardComponent,
  FormNavigationButtonsComponent,
} from '@shared/ui';
import { pacs008FormSteps } from '../pacs008.types';
import { FormRulesDirective } from '@helaba/iso20022-lib/directives';
import {
  FORM_METADATA,
  getErrorMessages,
} from '../../../../generated/pacs008-form-metadata';
import {
  RemittanceInformationFormSchema,
  RemittanceInformationFormValues,
  getFormGroup,
} from './remittance-information.form';
import { pacs008AffectedFields } from '@helaba/iso20022-lib/rules';
import { RemittanceInformationFormContentComponent } from './remittance-information-form-content';

@Component({
  selector: 'app-remittance-information',
  imports: [
    ReactiveFormsModule,
    FormRulesDirective,
    RemittanceInformationFormContentComponent,
    InfoCardComponent,
    FormNavigationButtonsComponent,
  ],
  templateUrl: './remittance-information.component.html',
  styleUrl: './remittance-information.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RemittanceInformationComponent extends FormPageComponent<
  RemittanceInformationFormSchema,
  RemittanceInformationFormValues
> {
  override pageKey = 'remittanceInformation';
  override formGroup = getFormGroup(this.fb);
  override formSteps = pacs008FormSteps;
  override affectedFields = pacs008AffectedFields;
  override formFields = FORM_METADATA[this.pageKey]
    .formFields as (keyof RemittanceInformationFormSchema)[];
  override validationRules = FORM_METADATA[this.pageKey].validationRules;

  override initializeErrorMessages(): void {
    const errorMessages = getErrorMessages(this.pageKey);
    this.formErrorsService.setErrorMessages(errorMessages);
  }
}
