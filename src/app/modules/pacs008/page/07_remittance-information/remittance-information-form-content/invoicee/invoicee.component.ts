import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { HiddenSectionComponent } from '@shared/ui';
import { PartyIdentification135__4InputComponent } from '../../../../components';

@Component({
  selector: 'app-invoicee',
  imports: [HiddenSectionComponent, PartyIdentification135__4InputComponent],
  templateUrl: './invoicee.component.html',
  styleUrl: './invoicee.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InvoiceeComponent {
  isReadOnly = input.required<boolean>();
  labels = input.required<Record<string, string>>();
}
