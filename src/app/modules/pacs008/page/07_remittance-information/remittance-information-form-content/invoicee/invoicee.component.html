<app-hidden-section
  header="Invoicee"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee']"
>
  <app-party-identification-135-4-input
    nameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Nm"
    [nameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Nm']
    "
    departmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Dept"
    [departmentLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Dept']
    "
    subDepartmentFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-SubDept"
    [subDepartmentLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-SubDept'
      ]
    "
    streetNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-StrtNm"
    [streetNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-StrtNm'
      ]
    "
    buildingNumberFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNb"
    [buildingNumberLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNb'
      ]
    "
    buildingNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNm"
    [buildingNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNm'
      ]
    "
    floorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Flr"
    [floorLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Flr']
    "
    postBoxFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstBx"
    [postBoxLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstBx']
    "
    roomFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Room"
    [roomLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Room']
    "
    postCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstCd"
    [postCodeLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstCd']
    "
    townNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm"
    [townNameLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm']
    "
    townLocationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm"
    [townLocationNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm'
      ]
    "
    districtNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-DstrctNm"
    [districtNameLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-DstrctNm'
      ]
    "
    countrySubdivisionFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn"
    [countrySubdivisionLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn'
      ]
    "
    countryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry"
    [countryLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry']
    "
    addressLineFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine"
    [addressLineLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine'
      ]
    "
    countryOfResidenceFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-CtryOfRes"
    [countryOfResidenceLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-CtryOfRes']
    "
    bicFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC"
    [bicLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC'
      ]
    "
    leiFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-LEI"
    [leiLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-LEI']
    "
    organisationIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id"
    [organisationIdLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id'
      ]
    "
    organisationIdSchemeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd"
    [organisationIdSchemeCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd'
      ]
    "
    proprietaryOrganisationIdSchemeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry"
    [proprietaryOrganisationIdSchemeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry'
      ]
    "
    organisationIdIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr"
    [organisationIdIssuerLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr'
      ]
    "
    birthDateFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt"
    [birthDateLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt'
      ]
    "
    provinceOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth"
    [provinceOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth'
      ]
    "
    cityOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth"
    [cityOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth'
      ]
    "
    countryOfBirthFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth"
    [countryOfBirthLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth'
      ]
    "
    individualIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id"
    [individualIdLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id'
      ]
    "
    individualIdSchemeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd"
    [individualIdSchemeCodeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd'
      ]
    "
    proprietaryIndividualIdSchemeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry"
    [proprietaryIndividualIdSchemeLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry'
      ]
    "
    individualIdIssuerFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr"
    [individualIdIssuerLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr'
      ]
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
