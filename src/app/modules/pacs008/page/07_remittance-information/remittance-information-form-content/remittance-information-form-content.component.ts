import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';
import { FormPageContentComponent, HiddenSectionComponent } from '@shared/ui';
import { FORM_METADATA } from '../../../../../generated/pacs008-form-metadata';
import { CBPR_RestrictedFINXMax140Text_ExtendedInputComponent } from '../../../components';
import { ReferredDocumentInformationComponent } from './referred-document-information';
import { ReferredDocumentAmountComponent } from './referred-document-amount';
import { CreditorReferenceInformationComponent } from './creditor-reference-information';
import { InvoicerComponent } from './invoicer';
import { InvoiceeComponent } from './invoicee';
import { TaxRemittanceComponent } from './tax-remittance';
import { GarnishmentRemittanceComponent } from './garnishment-remittance';
import { RelatedRemittanceInformationComponent } from './related-remittance-information';

@Component({
  selector: 'app-remittance-information-form-content',
  imports: [
    ReferredDocumentInformationComponent,
    ReferredDocumentAmountComponent,
    CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
    HiddenSectionComponent,
    CreditorReferenceInformationComponent,
    InvoicerComponent,
    InvoiceeComponent,
    TaxRemittanceComponent,
    GarnishmentRemittanceComponent,
    RelatedRemittanceInformationComponent,
  ],
  templateUrl: './remittance-information-form-content.component.html',
  styleUrl: './remittance-information-form-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RemittanceInformationFormContentComponent extends FormPageContentComponent {
  override pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();

  override labels = computed(() => FORM_METADATA[this.pageKey()].labels);
}
