import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { HiddenSectionComponent } from '@shared/ui';
import { PartyIdentification135__4InputComponent } from '../../../../components';

@Component({
  selector: 'app-invoicer',
  imports: [HiddenSectionComponent, PartyIdentification135__4InputComponent],
  templateUrl: './invoicer.component.html',
  styleUrl: './invoicer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InvoicerComponent {
  isReadOnly = input.required<boolean>();
  labels = input.required<Record<string, string>>();
}
