<app-cbpr-restricted-finx-max-140-text-extended-input
  fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Ustrd"
  [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Ustrd']"
  [isReadOnly]="isReadOnly()"
/>
<app-hidden-section
  header="Structured Remittance Information"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd']"
>
  <app-referred-document-information
    [labels]="labels()"
    [isReadOnly]="isReadOnly()"
  />
  <app-referred-document-amount
    [isReadOnly]="isReadOnly()"
    [labels]="labels()"
  />
  <app-creditor-reference-information
    [isReadOnly]="isReadOnly()"
    [labels]="labels()"
  />
  <app-invoicer [isReadOnly]="isReadOnly()" [labels]="labels()" />
  <app-invoicee [isReadOnly]="isReadOnly()" [labels]="labels()" />
  <app-tax-remittance [isReadOnly]="isReadOnly()" [labels]="labels()" />
  <app-garnishment-remittance [isReadOnly]="isReadOnly()" [labels]="labels()" />
  <app-cbpr-restricted-finx-max-140-text-extended-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf"
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf']"
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
<app-related-remittance-information
  [isReadOnly]="isReadOnly()"
  [labels]="labels()"
/>
