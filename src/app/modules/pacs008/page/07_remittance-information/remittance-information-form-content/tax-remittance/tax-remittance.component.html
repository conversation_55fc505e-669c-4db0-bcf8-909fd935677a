<app-hidden-section
  header="Tax Remittance"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt']"
>
  <app-hidden-section
    header="Creditor"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr']"
  >
    <app-cbpr-restricted-finx-max-35-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId"
      [label]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId']
      "
      [isReadOnly]="isReadOnly()"
    />
    <app-cbpr-restricted-finx-max-35-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId"
      [label]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId']
      "
      [isReadOnly]="isReadOnly()"
    />
    <app-cbpr-restricted-finx-max-35-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp"
      [label]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp']
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
  <app-hidden-section
    header="Debtor"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr']"
  >
    <app-tax-party-2-1-input
      taxIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxId"
      [taxIdLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxId']
      "
      registrationIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-RegnId"
      [registrationIdLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-RegnId']
      "
      taxTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxTp"
      [taxTypeLabel]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxTp']
      "
      authorisationTitleFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl"
      [authorisationTitleLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl'
        ]
      "
      authorisationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm"
      [authorisationNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
  <app-hidden-section
    header="Ultimate Debtor"
    i18n-header
    [errorScopes]="[
      'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr'
    ]"
  >
    <app-tax-party-2-1-input
      taxIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId"
      [taxIdLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId'
        ]
      "
      registrationIdFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId"
      [registrationIdLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId'
        ]
      "
      taxTypeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp"
      [taxTypeLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp'
        ]
      "
      authorisationTitleFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl"
      [authorisationTitleLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl'
        ]
      "
      authorisationNameFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm"
      [authorisationNameLabel]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
  <app-cbpr-restricted-finx-max-35-text-extended-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone"
    [label]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone']
    "
    [isReadOnly]="isReadOnly()"
  />
  <app-cbpr-restricted-finx-max-140-text-extended-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb"
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb']"
    [isReadOnly]="isReadOnly()"
  />
  <app-cbpr-restricted-finx-max-35-text-extended-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd"
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd']"
    [isReadOnly]="isReadOnly()"
  />
  <app-active-or-historic-currency-and-amount-input
    amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount"
    [amountLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount'
      ]
    "
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy"
    [currencyLabel]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy']
    "
    [isReadOnly]="isReadOnly()"
  />
  <app-active-or-historic-currency-and-amount-input
    amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount"
    [amountLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount'
      ]
    "
    currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy"
    [currencyLabel]="
      labels()[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy'
      ]
    "
    [isReadOnly]="isReadOnly()"
  />
  <app-iso-date-input
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dt']"
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dt"
    [isReadOnly]="isReadOnly()"
  />
  <app-form-text-input
    [label]="labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-SeqNb']"
    [fieldNames]="['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-SeqNb']"
    [isReadOnly]="isReadOnly()"
  />
  <!--TODO: array-->
  <app-hidden-section
    header="Tax Record"
    i18n-header
    [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd']"
  >
    <app-cbpr-restricted-finx-max-35-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp"
      [label]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp']
      "
      [isReadOnly]="isReadOnly()"
    />
    <app-cbpr-restricted-finx-max-35-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy"
      [label]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy']
      "
      [isReadOnly]="isReadOnly()"
    />
    <app-cbpr-restricted-finx-max-35-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls"
      [label]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
    <app-cbpr-restricted-finx-max-35-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts"
      [label]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
    <app-cbpr-restricted-finx-max-35-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId"
      [label]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId']
      "
      [isReadOnly]="isReadOnly()"
    />
    <app-cbpr-restricted-finx-max-35-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd"
      [label]="
        labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd']
      "
      [isReadOnly]="isReadOnly()"
    />
    <app-hidden-section
      header="Tax Period"
      i18n-header
      [errorScopes]="[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd'
      ]"
      ><app-tax-period-2-input
        yearFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr"
        [yearLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr'
          ]
        "
        typeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp"
        [typeLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp'
          ]
        "
        fromDateFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt"
        [fromDateLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt'
          ]
        "
        toDateFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt"
        [toDateLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt'
          ]
        "
        [isReadOnly]="isReadOnly()"
    /></app-hidden-section>
    <app-hidden-section
      header="Tax Amount"
      i18n-header
      [errorScopes]="[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt'
      ]"
    >
      <app-form-text-input
        [label]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate'
          ]
        "
        [fieldNames]="[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate'
        ]"
        [isReadOnly]="isReadOnly()"
      />
      <app-active-or-historic-currency-and-amount-input
        amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount"
        [amountLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount'
          ]
        "
        currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy"
        [currencyLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy'
          ]
        "
        [isReadOnly]="isReadOnly()"
      />
      <app-active-or-historic-currency-and-amount-input
        amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount"
        [amountLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount'
          ]
        "
        currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy"
        [currencyLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy'
          ]
        "
        [isReadOnly]="isReadOnly()"
      />
      <app-hidden-section
        header="Details"
        i18n-header
        [errorScopes]="[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls'
        ]"
      >
        <app-tax-period-2-input
          yearFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr"
          [yearLabel]="
            labels()[
              'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr'
            ]
          "
          typeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp"
          [typeLabel]="
            labels()[
              'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp'
            ]
          "
          fromDateFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt"
          [fromDateLabel]="
            labels()[
              'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt'
            ]
          "
          toDateFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt"
          [toDateLabel]="
            labels()[
              'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt'
            ]
          "
          [isReadOnly]="isReadOnly()"
        />
        <app-active-or-historic-currency-and-amount-input
          amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount"
          [amountLabel]="
            labels()[
              'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount'
            ]
          "
          currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy"
          [currencyLabel]="
            labels()[
              'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy'
            ]
          "
          [isReadOnly]="isReadOnly()"
        />
      </app-hidden-section>
    </app-hidden-section>
    <app-cbpr-restricted-finx-max-140-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf"
      [label]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
  </app-hidden-section>
</app-hidden-section>
