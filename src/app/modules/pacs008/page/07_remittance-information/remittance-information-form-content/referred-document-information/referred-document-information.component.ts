import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import {
  HiddenSectionComponent,
  OneOfSelectorComponent,
  FormTextInputComponent,
} from '@shared/ui';
import {
  CBPR_RestrictedFINXMax35Text_ExtendedInputComponent,
  ISODateInputComponent,
  ActiveOrHistoricCurrencyAndAmountInputComponent,
  DiscountAmountAndType1__1InputComponent,
  TaxAmountAndType1__1InputComponent,
  DocumentAdjustment1__1InputComponent,
} from '../../../../components';

@Component({
  selector: 'app-referred-document-information',
  imports: [
    HiddenSectionComponent,
    OneOfSelectorComponent,
    FormTextInputComponent,
    CBPR_RestrictedFINXMax35Text_ExtendedInputComponent,
    ISODateInputComponent,
    ActiveOrHistoricCurrencyAndAmountInputComponent,
    DiscountAmountAndType1__1InputComponent,
    TaxAmountAndType1__1InputComponent,
    DocumentAdjustment1__1InputComponent,
  ],
  templateUrl: './referred-document-information.component.html',
  styleUrl: './referred-document-information.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReferredDocumentInformationComponent {
  isReadOnly = input.required<boolean>();
  labels = input.required<Record<string, string>>();
}
