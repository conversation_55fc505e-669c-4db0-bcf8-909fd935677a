<app-hidden-section
  header="Referred Document Information"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf']"
>
  <app-one-of-selector
    header="Referred Document Type"
    i18n-header
    option1Label="Code"
    i18n-option1Label
    option2Label="Proprietary"
    i18n-option2Label
    [option1Fields]="[
      'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd'
    ]"
    [option2Fields]="[
      'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry'
    ]"
    [isReadOnly]="isReadOnly()"
  >
    <ng-container slot="option1">
      <app-form-text-input
        [label]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd'
          ]
        "
        [fieldNames]="[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd'
        ]"
        [isReadOnly]="isReadOnly()"
      />
    </ng-container>
    <ng-container slot="option2">
      <app-cbpr-restricted-finx-max-35-text-extended-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry"
        [label]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry'
          ]
        "
        [isReadOnly]="isReadOnly()"
      />
    </ng-container>
  </app-one-of-selector>
  <app-cbpr-restricted-finx-max-35-text-extended-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr"
    [label]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr']
    "
    [isReadOnly]="isReadOnly()"
  />
  <app-cbpr-restricted-finx-max-35-text-extended-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb"
    [label]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb']
    "
    [isReadOnly]="isReadOnly()"
  />
  <app-iso-date-input
    [label]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-RltdDt']
    "
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-RltdDt"
    [isReadOnly]="isReadOnly()"
  />
  <app-hidden-section
    header="Line Details"
    i18n-header
    [errorScopes]="[
      'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls'
    ]"
  >
    <app-one-of-selector
      header="Document Line Identification Type"
      i18n-header
      option1Label="Code"
      i18n-option1Label
      option2Label="Proprietary"
      i18n-option2Label
      [option1Fields]="[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd'
      ]"
      [option2Fields]="[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry'
      ]"
      [isReadOnly]="isReadOnly()"
    >
      <ng-container slot="option1">
        <app-form-text-input
          [label]="
            labels()[
              'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd'
            ]
          "
          [fieldNames]="[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd'
          ]"
          [isReadOnly]="isReadOnly()"
        />
      </ng-container>
      <ng-container slot="option2">
        <app-cbpr-restricted-finx-max-35-text-extended-input
          fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry"
          [label]="
            labels()[
              'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry'
            ]
          "
          [isReadOnly]="isReadOnly()"
        />
      </ng-container>
    </app-one-of-selector>
    <app-cbpr-restricted-finx-max-35-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr"
      [label]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
    <app-cbpr-restricted-finx-max-35-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb"
      [label]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
    <app-iso-date-input
      [label]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt'
        ]
      "
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt"
      [isReadOnly]="isReadOnly()"
    />
    <app-cbpr-restricted-finx-max-35-text-extended-input
      fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc"
      [label]="
        labels()[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc'
        ]
      "
      [isReadOnly]="isReadOnly()"
    />
    <app-hidden-section
      header="Amount"
      i18n-header
      [errorScopes]="[
        'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt'
      ]"
    >
      <app-active-or-historic-currency-and-amount-input
        amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount"
        [amountLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount'
          ]
        "
        currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy"
        [currencyLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy'
          ]
        "
        [isReadOnly]="isReadOnly()"
      />
      <app-discount-amount-and-type-1-1-input
        amountTypeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd"
        [amountTypeCodeLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd'
          ]
        "
        amountTypeProprietaryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry"
        [amountTypeProprietaryLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry'
          ]
        "
        amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount"
        [amountLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount'
          ]
        "
        currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy"
        [currencyLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy'
          ]
        "
        [isReadOnly]="isReadOnly()"
      />
      <app-active-or-historic-currency-and-amount-input
        amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount"
        [amountLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount'
          ]
        "
        currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy"
        [currencyLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy'
          ]
        "
        [isReadOnly]="isReadOnly()"
      />
      <app-tax-amount-and-type-1-1-input
        amountTypeCodeFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd"
        [amountTypeCodeLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd'
          ]
        "
        amountTypeProprietaryFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry"
        [amountTypeProprietaryLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry'
          ]
        "
        amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount"
        [amountLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount'
          ]
        "
        currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy"
        [currencyLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy'
          ]
        "
        [isReadOnly]="isReadOnly()"
      />
      <app-document-adjustment-1-1-input
        amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount"
        [amountLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount'
          ]
        "
        currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy"
        [currencyLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy'
          ]
        "
        creditDebitIndicatorFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd"
        [creditDebitIndicatorLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd'
          ]
        "
        reasonFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn"
        [reasonLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn'
          ]
        "
        additionalInformationFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf"
        [additionalInformationLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf'
          ]
        "
        [isReadOnly]="isReadOnly()"
      />
      <app-active-or-historic-currency-and-amount-input
        amountFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount"
        [amountLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount'
          ]
        "
        currencyFieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy"
        [currencyLabel]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy'
          ]
        "
        [isReadOnly]="isReadOnly()"
      />
    </app-hidden-section>
  </app-hidden-section>
</app-hidden-section>
