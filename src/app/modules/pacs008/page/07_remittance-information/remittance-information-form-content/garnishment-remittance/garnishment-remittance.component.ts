import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import {
  HiddenSectionComponent,
  OneOfSelectorComponent,
  FormTextInputComponent,
} from '@shared/ui';
import {
  CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
  CBPR_RestrictedFINXMax35Text_ExtendedInputComponent,
  ISODateInputComponent,
  ActiveOrHistoricCurrencyAndAmountInputComponent,
  PartyIdentification135__4InputComponent,
  TrueFalseIndicatorInputComponent,
} from '../../../../components';

@Component({
  selector: 'app-garnishment-remittance',
  imports: [
    CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
    HiddenSectionComponent,
    OneOfSelectorComponent,
    FormTextInputComponent,
    CBPR_RestrictedFINXMax35Text_ExtendedInputComponent,
    ISODateInputComponent,
    ActiveOrHistoricCurrencyAndAmountInputComponent,
    PartyIdentification135__4InputComponent,
    TrueFalseIndicatorInputComponent,
  ],
  templateUrl: './garnishment-remittance.component.html',
  styleUrl: './garnishment-remittance.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GarnishmentRemittanceComponent {
  isReadOnly = input.required<boolean>();
  labels = input.required<Record<string, string>>();
}
