import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import {
  HiddenSectionComponent,
  OneOfSelectorComponent,
  FormTextInputComponent,
} from '@shared/ui';
import { CBPR_RestrictedFINXMax35Text_ExtendedInputComponent } from '../../../../components';

@Component({
  selector: 'app-creditor-reference-information',
  imports: [
    HiddenSectionComponent,
    OneOfSelectorComponent,
    FormTextInputComponent,
    CBPR_RestrictedFINXMax35Text_ExtendedInputComponent,
  ],
  templateUrl: './creditor-reference-information.component.html',
  styleUrl: './creditor-reference-information.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreditorReferenceInformationComponent {
  isReadOnly = input.required<boolean>();
  labels = input.required<Record<string, string>>();
}
