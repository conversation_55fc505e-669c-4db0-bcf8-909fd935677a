<app-hidden-section
  header="Creditor Reference Information"
  i18n-header
  [errorScopes]="['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf']"
>
  <app-one-of-selector
    header="Creditor Reference Type"
    i18n-header
    option1Label="Code"
    i18n-option1Label
    option2Label="Proprietary"
    i18n-option2Label
    [option1Fields]="[
      'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd'
    ]"
    [option2Fields]="[
      'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry'
    ]"
    [isReadOnly]="isReadOnly()"
  >
    <ng-template slot="option1">
      <app-form-text-input
        [label]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd'
          ]
        "
        [fieldNames]="[
          'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd'
        ]"
        [isReadOnly]="isReadOnly()"
      />
    </ng-template>
    <ng-template slot="option2">
      <app-cbpr-restricted-finx-max-35-text-extended-input
        fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry"
        [label]="
          labels()[
            'FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry'
          ]
        "
        [isReadOnly]="isReadOnly()"
      />
    </ng-template>
  </app-one-of-selector>
  <app-cbpr-restricted-finx-max-35-text-extended-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr"
    [label]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr']
    "
    [isReadOnly]="isReadOnly()"
  />
  <app-cbpr-restricted-finx-max-35-text-extended-input
    fieldName="FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref"
    [label]="
      labels()['FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref']
    "
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
