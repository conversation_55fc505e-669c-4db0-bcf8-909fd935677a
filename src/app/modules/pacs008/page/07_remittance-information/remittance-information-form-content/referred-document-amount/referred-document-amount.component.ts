import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { HiddenSectionComponent } from '@shared/ui';
import {
  ActiveOrHistoricCurrencyAndAmountInputComponent,
  DiscountAmountAndType1__1InputComponent,
  TaxAmountAndType1__1InputComponent,
  DocumentAdjustment1__1InputComponent,
} from '../../../../components';

@Component({
  selector: 'app-referred-document-amount',
  imports: [
    HiddenSectionComponent,
    ActiveOrHistoricCurrencyAndAmountInputComponent,
    DiscountAmountAndType1__1InputComponent,
    TaxAmountAndType1__1InputComponent,
    DocumentAdjustment1__1InputComponent,
  ],
  templateUrl: './referred-document-amount.component.html',
  styleUrl: './referred-document-amount.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReferredDocumentAmountComponent {
  isReadOnly = input.required<boolean>();
  labels = input.required<Record<string, string>>();
}
