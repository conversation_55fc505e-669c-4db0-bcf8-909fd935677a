import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
} from '@angular/core';
import {
  FormPageContentComponent,
  HiddenSectionComponent,
  FormTextInputComponent,
  FormGroupArrayComponent,
} from '@shared/ui';
import { FORM_METADATA } from '../../../../../generated/pacs008-form-metadata';
import {
  CashAccount38__1InputComponent,
  PartyIdentification135__1InputComponent,
  BranchAndFinancialInstitutionIdentification6__1InputComponent,
  FinancialInstitutionIdentification18__1InputComponent,
  CBPR_RestrictedFINXMax35TextInputComponent,
  BranchAndFinancialInstitutionIdentification6__2InputComponent,
  CBPR_RestrictedFINXMax140TextInputComponent,
} from '../../../components';
import {
  ControlContainer,
  FormGroup,
  NonNullableFormBuilder,
  ReactiveFormsModule,
} from '@angular/forms';
import { createInstrForCdtrAgtGroup } from '../financial-institution-information.form';

@Component({
  selector: 'app-financial-institution-information-form-content',
  imports: [
    HiddenSectionComponent,
    CashAccount38__1InputComponent,
    PartyIdentification135__1InputComponent,
    BranchAndFinancialInstitutionIdentification6__1InputComponent,
    FinancialInstitutionIdentification18__1InputComponent,
    CBPR_RestrictedFINXMax35TextInputComponent,
    BranchAndFinancialInstitutionIdentification6__2InputComponent,
    FormTextInputComponent,
    CBPR_RestrictedFINXMax140TextInputComponent,
    FormGroupArrayComponent,
    ReactiveFormsModule,
  ],
  templateUrl:
    './financial-institution-information-form-content.component.html',
  styleUrl: './financial-institution-information-form-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinancialInstitutionInformationFormContentComponent extends FormPageContentComponent {
  private fb = inject(NonNullableFormBuilder);
  controlContainer = inject(ControlContainer);

  get parentFormGroup(): FormGroup {
    return this.controlContainer.control as FormGroup;
  }

  override pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();

  override labels = computed(() => FORM_METADATA[this.pageKey()].labels);

  getInstrForCdtrAgtGroupFactory() {
    return () => createInstrForCdtrAgtGroup(this.fb);
  }
}
