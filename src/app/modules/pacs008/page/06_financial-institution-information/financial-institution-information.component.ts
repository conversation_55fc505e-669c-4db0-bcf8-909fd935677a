import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  FormPageComponent,
  InfoCardComponent,
  FormNavigationButtonsComponent,
} from '@shared/ui';
import { pacs008FormSteps } from '../pacs008.types';
import { FormRulesDirective } from '@helaba/iso20022-lib/directives';
import {
  FORM_METADATA,
  getErrorMessages,
} from '../../../../generated/pacs008-form-metadata';
import {
  FinancialInstitutionInformationFormSchema,
  FinancialInstitutionInformationFormValues,
  getFormGroup,
} from './financial-institution-information.form';
import { pacs008AffectedFields } from '@helaba/iso20022-lib/rules';
import { FinancialInstitutionInformationFormContentComponent } from './financial-institution-information-form-content';

@Component({
  selector: 'app-financial-institution-information',
  imports: [
    ReactiveFormsModule,
    FormRulesDirective,
    FinancialInstitutionInformationFormContentComponent,
    InfoCardComponent,
    FormNavigationButtonsComponent,
  ],
  templateUrl: './financial-institution-information.component.html',
  styleUrl: './financial-institution-information.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinancialInstitutionInformationComponent extends FormPageComponent<
  FinancialInstitutionInformationFormSchema,
  FinancialInstitutionInformationFormValues
> {
  override pageKey = 'financialInstitutionInformation';
  override formGroup = getFormGroup(this.fb);
  override formSteps = pacs008FormSteps;
  override affectedFields = pacs008AffectedFields;
  override formFields = FORM_METADATA[this.pageKey]
    .formFields as (keyof FinancialInstitutionInformationFormSchema)[];
  override validationRules = FORM_METADATA[this.pageKey].validationRules;

  override initializeErrorMessages(): void {
    const errorMessages = getErrorMessages(this.pageKey);
    this.formErrorsService.setErrorMessages(errorMessages);
  }
}
