import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { ReviewSubmitPageComponent } from '@shared/ui';
import { ReviewSubmitPageContentComponent } from '@shared/ui';
import { pacs008FormPageTitles } from '../pacs008.types';
import { PaymentBasicsFormContentComponent } from '../01_payment-basics';
import { AmountCurrencyFormContentComponent } from '../02_amount-currency';
import { SettlementInformationFormContentComponent } from '../03_settlement-information';
import { DebtorInformationFormContentComponent } from '../04_debtor-information';
import { CreditorInformationFormContentComponent } from '../05_creditor-information';
import { FinancialInstitutionInformationFormContentComponent } from '../06_financial-institution-information';
import { RemittanceInformationFormContentComponent } from '../07_remittance-information';
import { RegulatoryReportingFormContentComponent } from '../08_regulatory-reporting';

@Component({
  selector: 'app-pacs008-review-submit',
  imports: [
    ReactiveFormsModule,
    ReviewSubmitPageContentComponent,
    PaymentBasicsFormContentComponent,
    AmountCurrencyFormContentComponent,
    SettlementInformationFormContentComponent,
    DebtorInformationFormContentComponent,
    CreditorInformationFormContentComponent,
    FinancialInstitutionInformationFormContentComponent,
    RemittanceInformationFormContentComponent,
    RegulatoryReportingFormContentComponent,
  ],
  templateUrl: './pacs008-review-submit.component.html',
  styleUrl: './pacs008-review-submit.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Pacs008ReviewSubmitComponent extends ReviewSubmitPageComponent {
  override formPageTitles: Record<string, string> = pacs008FormPageTitles;
}
