<app-review-submit-page-content [formPageTitles]="formPageTitles">
  <ng-template let-pageKey let-formGroup="formGroup">
    @switch (pageKey) { @case ("paymentBasics") {
    <form [formGroup]="formGroup">
      <app-payment-basics-form-content
        pageKey="paymentBasics"
        [isReadOnly]="true"
      />
    </form>
    } @case ("amountCurrency") {
    <form [formGroup]="formGroup">
      <app-amount-currency-form-content
        pageKey="amountCurrency"
        [isReadOnly]="true"
      />
    </form>
    } @case ("settlementInformation") {
    <form [formGroup]="formGroup">
      <app-settlement-information-form-content
        pageKey="settlementInformation"
        [isReadOnly]="true"
      />
    </form>
    } @case ("debtorInformation") {
    <form [formGroup]="formGroup">
      <app-debtor-information-form-content
        pageKey="debtorInformation"
        [isReadOnly]="true"
      />
    </form>
    } @case ("creditorInformation") {
    <form [formGroup]="formGroup">
      <app-creditor-information-form-content
        pageKey="creditorInformation"
        [isReadOnly]="true"
      />
    </form>
    } @case ("financialInstitutionInformation") {
    <form [formGroup]="formGroup">
      <app-financial-institution-information-form-content
        pageKey="financialInstitutionInformation"
        [isReadOnly]="true"
      />
    </form>
    } @case ("remittanceInformation") {
    <form [formGroup]="formGroup">
      <app-remittance-information-form-content
        pageKey="remittanceInformation"
        [isReadOnly]="true"
      />
    </form>
    } @case ("regulatoryReporting") {
    <form [formGroup]="formGroup">
      <app-regulatory-reporting-form-content
        pageKey="regulatoryReporting"
        [isReadOnly]="true"
      />
    </form>
    } }
  </ng-template>
</app-review-submit-page-content>
