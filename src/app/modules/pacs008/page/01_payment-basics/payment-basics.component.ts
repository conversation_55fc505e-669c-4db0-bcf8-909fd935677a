import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import {
  FormPageComponent,
  InfoCardComponent,
  FormNavigationButtonsComponent,
  FormTextInputComponent,
  FormGroupArrayComponent,
} from '@shared/ui';
import { pacs008FormSteps } from '../pacs008.types';
import { FormRulesDirective } from '@helaba/iso20022-lib/directives';
import {
  FORM_METADATA,
  getErrorMessages,
} from '../../../../generated/pacs008-form-metadata';
import {
  getFormGroup,
  PaymentBasicsFormSchema,
  PaymentBasicsFormValues,
} from './payment-basics.form';
import { pacs008AffectedFields } from '@helaba/iso20022-lib/rules';
import { PaymentBasicsFormContentComponent } from './payment-basics-form-content';

@Component({
  selector: 'app-payment-basics',
  imports: [
    ReactiveFormsModule,
    FormRulesDirective,
    PaymentBasicsFormContentComponent,
    InfoCardComponent,
    FormNavigationButtonsComponent,
    FormTextInputComponent,
    FormGroupArrayComponent,
  ],
  templateUrl: './payment-basics.component.html',
  styleUrl: './payment-basics.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PaymentBasicsComponent extends FormPageComponent<
  PaymentBasicsFormSchema,
  PaymentBasicsFormValues
> {
  override pageKey = 'paymentBasics';
  override formGroup = getFormGroup(this.fb);
  override formSteps = pacs008FormSteps;
  override affectedFields = pacs008AffectedFields;
  override formFields = FORM_METADATA[this.pageKey]
    .formFields as (keyof PaymentBasicsFormSchema)[];
  override validationRules = FORM_METADATA[this.pageKey].validationRules;

  override initializeErrorMessages(): void {
    const errorMessages = getErrorMessages(this.pageKey);
    this.formErrorsService.setErrorMessages(errorMessages);
  }

  testFormGroup = this.fb.group({
    testField: this.fb.control(''),
    nestedTestField: this.fb.array<
      FormGroup<{
        Test1: FormControl<string>;
        Test2: FormControl<string>;
      }>
    >([]),
  });

  getTestGroupFactory() {
    return () =>
      this.fb.group({
        Test1: this.fb.control(''),
        Test2: this.fb.control(''),
      });
  }
}
