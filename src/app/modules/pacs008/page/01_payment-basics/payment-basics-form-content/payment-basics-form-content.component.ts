import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';
import {
  FormPageContentComponent,
  FormRadioButtonGroupComponent,
  FormTextInputComponent,
  SelectOption,
  HiddenSectionComponent,
  OneOfSelectorComponent,
} from '@shared/ui';
import { FORM_METADATA } from '../../../../../generated/pacs008-form-metadata';
import { CBPR_RestrictedFINXMax35TextInputComponent } from '../../../components';
import {
  ClearingChannel2Code,
  clearingChannel2Codes,
  Priority2Code,
  priority2Codes,
} from '@shared/types';

@Component({
  selector: 'app-payment-basics-form-content',
  imports: [
    FormTextInputComponent,
    CBPR_RestrictedFINXMax35TextInputComponent,
    FormRadioButtonGroupComponent,
    HiddenSectionComponent,
    OneOfSelectorComponent,
  ],
  templateUrl: './payment-basics-form-content.component.html',
  styleUrl: './payment-basics-form-content.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PaymentBasicsFormContentComponent extends FormPageContentComponent {
  override pageKey = input.required<string>();
  isReadOnly = input.required<boolean>();

  override labels = computed(() => FORM_METADATA[this.pageKey()].labels);

  settlementPriorityOptions: SelectOption<Priority2Code>[] = priority2Codes.map(
    (code) => ({
      key: code,
      label: code,
      value: code,
    })
  );
  clearingChannelOptions: SelectOption<ClearingChannel2Code>[] =
    clearingChannel2Codes.map((code) => ({
      key: code,
      label: code,
      value: code,
    }));
}
