import { NonNullableFormBuilder } from '@angular/forms';
import { FormValues } from '@shared/types';

export function getFormSchema(fb: NonNullableFormBuilder) {
  return {
    'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-InstrId': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-EndToEndId': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-TxId': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Cd': fb.control(''),
    'FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Prtry': fb.control(''),
  };
}

export type PaymentBasicsFormSchema = ReturnType<typeof getFormSchema>;

export type PaymentBasicsFormValues = FormValues<PaymentBasicsFormSchema>;

export function getFormGroup(fb: NonNullableFormBuilder) {
  const schema = getFormSchema(fb);
  return fb.group(schema);
}
