<app-info-card i18n>
  Configure basic information about the transaction and the payment, such as the
  service level and the category purpose.
</app-info-card>

<form [formGroup]="testFormGroup">
  <h2>Edit</h2>
  <app-form-text-input
    label="testField"
    [fieldNames]="['testField']"
    [isReadOnly]="false"
  />
  <app-form-group-array
    label="nestedFieldName"
    [fieldNames]="['nestedTestField']"
    [isReadOnly]="false"
    [groupFactory]="getTestGroupFactory()"
    [itemTemplate]="testTemplate"
  />
  <h2>View</h2>
  <app-form-text-input
    label="testField"
    [fieldNames]="['testField']"
    [isReadOnly]="true"
  />
  <app-form-group-array
    label="nestedFieldName"
    [fieldNames]="['nestedTestField']"
    [isReadOnly]="true"
    [groupFactory]="getTestGroupFactory()"
    [itemTemplate]="testTemplate"
  />
</form>

<!-- <form
  [formGroup]="formGroup"
  [formRules]="validationRules"
  [affectedFields]="affectedFields"
>
  <app-payment-basics-form-content [pageKey]="pageKey" [isReadOnly]="false" />
</form> -->
<app-form-navigation-buttons [steps]="formSteps" [formGroup]="formGroup" />

<!-- <ng-template
  #testTemplate
  let-group
  let-index="index"
  let-fieldPrefix="fieldPrefix"
>
  <app-form-text-input
    [fieldNames]="['Test1']"
    label="Test 1"
    [isReadOnly]="false"
  />
  <app-form-text-input
    [fieldNames]="['Test2']"
    label="Test 2"
    [isReadOnly]="false"
  />
</ng-template> -->

<ng-template #testTemplate let-index let-control="control" let-fieldPrefix>
  <div
    [formGroup]="control"
    style="border: 1px solid red; padding: 10px; margin: 10px"
  >
    <div>
      <label for="Test1-{{ index }}">Test 1:</label>
      <input
        id="Test1-{{ index }}"
        type="text"
        formControlName="Test1"
        style="margin: 5px"
      />
    </div>

    <div>
      <label for="Test2-{{ index }}">Test 2:</label>
      <input
        id="Test2-{{ index }}"
        type="text"
        formControlName="Test2"
        style="margin: 5px"
      />
    </div>
  </div>
</ng-template>
