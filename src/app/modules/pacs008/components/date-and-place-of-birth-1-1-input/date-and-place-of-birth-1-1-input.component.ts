import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { ISODateInputComponent } from '../iso-date-input';
import { CBPR_RestrictedFINXMax35Text_ExtendedInputComponent } from '../cbpr-restricted-finx-max-35-text-extended-input';
import { CountryCodeInputComponent } from '../country-code-input';

@Component({
  selector: 'app-date-and-place-of-birth-1-1-input',
  imports: [
    ISODateInputComponent,
    CBPR_RestrictedFINXMax35Text_ExtendedInputComponent,
    CountryCodeInputComponent,
  ],
  templateUrl: './date-and-place-of-birth-1-1-input.component.html',
  styleUrl: './date-and-place-of-birth-1-1-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DateAndPlaceOfBirth1__1InputComponent {
  birthDateFieldName = input.required<string>();
  birthDateLabel = input.required<string>();
  provinceOfBirthFieldName = input.required<string>();
  provinceOfBirthLabel = input.required<string>();
  cityOfBirthFieldName = input.required<string>();
  cityOfBirthLabel = input.required<string>();
  countryOfBirthFieldName = input.required<string>();
  countryOfBirthLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
