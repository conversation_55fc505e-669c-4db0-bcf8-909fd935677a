import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormTextInputComponent } from '../../../shared/ui/form/form-fields/form-text-input/form-text-input.component';

@Component({
  selector: 'app-lei-identifier-input',
  imports: [FormTextInputComponent],
  templateUrl: './lei-identifier-input.component.html',
  styleUrl: './lei-identifier-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LEIIdentifierInputComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  isReadOnly = input.required<boolean>();
}
