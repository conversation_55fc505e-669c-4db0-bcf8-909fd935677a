import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { CBPR_RestrictedFINXMax35Text_ExtendedInputComponent } from '../cbpr-restricted-finx-max-35-text-extended-input';
import { CBPR_RestrictedFINXMax140Text_ExtendedInputComponent } from '../cbpr-restricted-finx-max-140-text-extended-input';

@Component({
  selector: 'app-tax-party-2-1-input',
  imports: [
    CBPR_RestrictedFINXMax35Text_ExtendedInputComponent,
    CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
  ],
  templateUrl: './tax-party-2-1-input.component.html',
  styleUrl: './tax-party-2-1-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TaxParty2__1InputComponent {
  taxIdFieldName = input.required<string>();
  taxIdLabel = input.required<string>();
  registrationIdFieldName = input.required<string>();
  registrationIdLabel = input.required<string>();
  taxTypeFieldName = input.required<string>();
  taxTypeLabel = input.required<string>();
  authorisationTitleFieldName = input.required<string>();
  authorisationTitleLabel = input.required<string>();
  authorisationNameFieldName = input.required<string>();
  authorisationNameLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
