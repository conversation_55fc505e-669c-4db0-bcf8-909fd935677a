import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { BICFIDec2014IdentifierInputComponent } from '../bicfi-dec-2014-identifier-input/bicfi-dec-2014-identifier-input.component';
import { ClearingSystemMemberIdentification2__1InputComponent } from '../clearing-system-member-identification-2-1-input/clearing-system-member-identification-2-1-input.component';
import { LEIIdentifierInputComponent } from '../lei-identifier-input/lei-identifier-input.component';
import { CBPR_RestrictedFINXMax140Text_ExtendedInputComponent } from '../cbpr-restricted-finx-max-140-text-extended-input/cbpr-restricted-finx-max-140-text-extended-input.component';
import { PostalAddress24__1InputComponent } from '../postal-address-24-1-input/postal-address-24-1-input.component';
import { HiddenSectionComponent } from '../../../shared/ui/form/form-structures/hidden-section/hidden-section.component';

@Component({
  selector: 'app-financial-institution-identification-18-1-input',
  imports: [
    BICFIDec2014IdentifierInputComponent,
    ClearingSystemMemberIdentification2__1InputComponent,
    LEIIdentifierInputComponent,
    CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
    PostalAddress24__1InputComponent,
    HiddenSectionComponent,
  ],
  templateUrl:
    './financial-institution-identification-18-1-input.component.html',
  styleUrl: './financial-institution-identification-18-1-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinancialInstitutionIdentification18__1InputComponent {
  bicfiFieldName = input.required<string>();
  bicfiLabel = input.required<string>();
  clearingSystemIdCodeFieldName = input.required<string>();
  clearingSystemIdCodeLabel = input.required<string>();
  clearingSystemMemberIdFieldName = input.required<string>();
  clearingSystemMemberIdLabel = input.required<string>();
  leiIdentifierFieldName = input.required<string>();
  leiIdentifierLabel = input.required<string>();
  nameFieldName = input.required<string>();
  nameLabel = input.required<string>();
  departmentFieldName = input.required<string>();
  departmentLabel = input.required<string>();
  subDepartmentFieldName = input.required<string>();
  subDepartmentLabel = input.required<string>();
  streetNameFieldName = input.required<string>();
  streetNameLabel = input.required<string>();
  buildingNumberFieldName = input.required<string>();
  buildingNumberLabel = input.required<string>();
  buildingNameFieldName = input.required<string>();
  buildingNameLabel = input.required<string>();
  floorFieldName = input.required<string>();
  floorLabel = input.required<string>();
  postBoxFieldName = input.required<string>();
  postBoxLabel = input.required<string>();
  roomFieldName = input.required<string>();
  roomLabel = input.required<string>();
  postCodeFieldName = input.required<string>();
  postCodeLabel = input.required<string>();
  townNameFieldName = input.required<string>();
  townNameLabel = input.required<string>();
  townLocationNameFieldName = input.required<string>();
  townLocationNameLabel = input.required<string>();
  districtNameFieldName = input.required<string>();
  districtNameLabel = input.required<string>();
  countrySubdivisionFieldName = input.required<string>();
  countrySubdivisionLabel = input.required<string>();
  countryFieldName = input.required<string>();
  countryLabel = input.required<string>();
  addressLineFieldName = input.required<string>();
  addressLineLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
