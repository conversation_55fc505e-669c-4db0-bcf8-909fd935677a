<!-- BICFI -->
<app-bicfi-dec-2014-identifier-input
  [fieldName]="bicfiFieldName()"
  [label]="bicfiLabel()"
  [isReadOnly]="isReadOnly()"
/>
<!-- ClrSysMmbId -->
<app-clearing-system-member-identification-2-1-input
  [clearingSystemIdCodeFieldName]="clearingSystemIdCodeFieldName()"
  [clearingSystemIdCodeLabel]="clearingSystemIdCodeLabel()"
  [clearingSystemMemberIdFieldName]="clearingSystemMemberIdFieldName()"
  [clearingSystemMemberIdLabel]="clearingSystemMemberIdLabel()"
  [isReadOnly]="isReadOnly()"
/>
<!-- LEI -->
<app-lei-identifier-input
  [fieldName]="leiIdentifierFieldName()"
  [label]="leiIdentifierLabel()"
  [isReadOnly]="isReadOnly()"
/>
<!-- Nm -->
<app-cbpr-restricted-finx-max-140-text-extended-input
  [fieldName]="nameFieldName()"
  [label]="nameLabel()"
  [isReadOnly]="isReadOnly()"
/>
<!-- PstlAdr -->
<app-hidden-section
  header="Adresse"
  [errorScopes]="[
    departmentFieldName(),
    subDepartmentFieldName(),
    streetNameFieldName(),
    buildingNumberFieldName(),
    buildingNameFieldName(),
    floorFieldName(),
    postBoxFieldName(),
    roomFieldName(),
    postCodeFieldName(),
    townNameFieldName(),
    townLocationNameFieldName(),
    districtNameFieldName(),
    countrySubdivisionFieldName(),
    countryFieldName(),
    addressLineFieldName()
  ]"
>
  <app-postal-address-24-1-input
    [departmentFieldName]="departmentFieldName()"
    [departmentLabel]="departmentLabel()"
    [subDepartmentFieldName]="subDepartmentFieldName()"
    [subDepartmentLabel]="subDepartmentLabel()"
    [streetNameFieldName]="streetNameFieldName()"
    [streetNameLabel]="streetNameLabel()"
    [buildingNumberFieldName]="buildingNumberFieldName()"
    [buildingNumberLabel]="buildingNumberLabel()"
    [buildingNameFieldName]="buildingNameFieldName()"
    [buildingNameLabel]="buildingNameLabel()"
    [floorFieldName]="floorFieldName()"
    [floorLabel]="floorLabel()"
    [postBoxFieldName]="postBoxFieldName()"
    [postBoxLabel]="postBoxLabel()"
    [roomFieldName]="roomFieldName()"
    [roomLabel]="roomLabel()"
    [postCodeFieldName]="postCodeFieldName()"
    [postCodeLabel]="postCodeLabel()"
    [townNameFieldName]="townNameFieldName()"
    [townNameLabel]="townNameLabel()"
    [townLocationNameFieldName]="townLocationNameFieldName()"
    [townLocationNameLabel]="townLocationNameLabel()"
    [districtNameFieldName]="districtNameFieldName()"
    [districtNameLabel]="districtNameLabel()"
    [countrySubdivisionFieldName]="countrySubdivisionFieldName()"
    [countrySubdivisionLabel]="countrySubdivisionLabel()"
    [countryFieldName]="countryFieldName()"
    [countryLabel]="countryLabel()"
    [addressLineFieldName]="addressLineFieldName()"
    [addressLineLabel]="addressLineLabel()"
    [isReadOnly]="isReadOnly()"
  />
</app-hidden-section>
