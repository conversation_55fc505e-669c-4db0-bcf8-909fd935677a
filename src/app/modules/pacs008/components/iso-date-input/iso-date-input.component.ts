import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormDateInputComponent } from '@shared/ui';

@Component({
  selector: 'app-iso-date-input',
  imports: [FormDateInputComponent],
  templateUrl: './iso-date-input.component.html',
  styleUrl: './iso-date-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ISODateInputComponent {
  label = input.required<string>();
  fieldName = input.required<string>();
  isReadOnly = input.required<boolean>();
}
