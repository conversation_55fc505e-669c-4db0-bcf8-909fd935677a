import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { ActiveOrHistoricCurrencyAndAmountInputComponent } from '../active-or-historic-currency-and-amount-input';
import {
  FormTextInputComponent,
  FormRadioButtonGroupComponent,
  SelectOption,
} from '@shared/ui';
import { CBPR_RestrictedFINXMax140Text_ExtendedInputComponent } from '../cbpr-restricted-finx-max-140-text-extended-input';
import { CreditDebitCode, creditDebitCodes } from '@shared/types';

@Component({
  selector: 'app-document-adjustment-1-1-input',
  imports: [
    ActiveOrHistoricCurrencyAndAmountInputComponent,
    FormTextInputComponent,
    CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
    FormRadioButtonGroupComponent,
  ],
  templateUrl: './document-adjustment-1-1-input.component.html',
  styleUrl: './document-adjustment-1-1-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentAdjustment1__1InputComponent {
  amountFieldName = input.required<string>();
  amountLabel = input.required<string>();
  currencyFieldName = input.required<string>();
  currencyLabel = input.required<string>();
  creditDebitIndicatorFieldName = input.required<string>();
  creditDebitIndicatorLabel = input.required<string>();
  reasonFieldName = input.required<string>();
  reasonLabel = input.required<string>();
  additionalInformationFieldName = input.required<string>();
  additionalInformationLabel = input.required<string>();

  isReadOnly = input.required<boolean>();

  creditDebitCodeOptions: SelectOption<CreditDebitCode>[] =
    creditDebitCodes.map((code) => ({
      key: code,
      label: code,
      value: code,
    }));
}
