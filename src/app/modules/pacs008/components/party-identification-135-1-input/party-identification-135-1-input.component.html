<!-- Nm -->
<app-cbpr-restricted-finx-max-140-text-extended-input
  [fieldName]="nameFieldName()"
  [label]="nameLabel()"
  [isReadOnly]="isReadOnly()"
/>
<!-- PstlAdr -->
<app-postal-address-24-2-input
  [departmentFieldName]="departmentFieldName()"
  [departmentLabel]="departmentLabel()"
  [subDepartmentFieldName]="subDepartmentFieldName()"
  [subDepartmentLabel]="subDepartmentLabel()"
  [streetNameFieldName]="streetNameFieldName()"
  [streetNameLabel]="streetNameLabel()"
  [buildingNumberFieldName]="buildingNumberFieldName()"
  [buildingNumberLabel]="buildingNumberLabel()"
  [buildingNameFieldName]="buildingNameFieldName()"
  [buildingNameLabel]="buildingNameLabel()"
  [floorFieldName]="floorFieldName()"
  [floorLabel]="floorLabel()"
  [postBoxFieldName]="postBoxFieldName()"
  [postBoxLabel]="postBoxLabel()"
  [roomFieldName]="roomFieldName()"
  [roomLabel]="roomLabel()"
  [postCodeFieldName]="postCodeFieldName()"
  [postCodeLabel]="postCodeLabel()"
  [townNameFieldName]="townNameFieldName()"
  [townNameLabel]="townNameLabel()"
  [townLocationNameFieldName]="townLocationNameFieldName()"
  [townLocationNameLabel]="townLocationNameLabel()"
  [districtNameFieldName]="districtNameFieldName()"
  [districtNameLabel]="districtNameLabel()"
  [countrySubdivisionFieldName]="countrySubdivisionFieldName()"
  [countrySubdivisionLabel]="countrySubdivisionLabel()"
  [countryFieldName]="countryFieldName()"
  [countryLabel]="countryLabel()"
  [addressLineFieldName]="addressLineFieldName()"
  [addressLineLabel]="addressLineLabel()"
  [isReadOnly]="isReadOnly()"
/>
<!-- Id -->
<app-party-38-choice-1-input
  [bicFieldName]="bicFieldName()"
  [bicLabel]="bicLabel()"
  [leiFieldName]="leiFieldName()"
  [leiLabel]="leiLabel()"
  [organisationIdFieldName]="organisationIdFieldName()"
  [organisationIdLabel]="organisationIdLabel()"
  [organisationIdSchemeCodeFieldName]="organisationIdSchemeCodeFieldName()"
  [organisationIdSchemeCodeLabel]="organisationIdSchemeCodeLabel()"
  [proprietaryOrganisationIdSchemeFieldName]="
    proprietaryOrganisationIdSchemeFieldName()
  "
  [proprietaryOrganisationIdSchemeLabel]="
    proprietaryOrganisationIdSchemeLabel()
  "
  [organisationIdIssuerFieldName]="organisationIdIssuerFieldName()"
  [organisationIdIssuerLabel]="organisationIdIssuerLabel()"
  [birthDateFieldName]="birthDateFieldName()"
  [birthDateLabel]="birthDateLabel()"
  [provinceOfBirthFieldName]="provinceOfBirthFieldName()"
  [provinceOfBirthLabel]="provinceOfBirthLabel()"
  [cityOfBirthFieldName]="cityOfBirthFieldName()"
  [cityOfBirthLabel]="cityOfBirthLabel()"
  [countryOfBirthFieldName]="countryOfBirthFieldName()"
  [countryOfBirthLabel]="countryOfBirthLabel()"
  [individualIdFieldName]="individualIdFieldName()"
  [individualIdLabel]="individualIdLabel()"
  [individualIdSchemeCodeFieldName]="individualIdSchemeCodeFieldName()"
  [individualIdSchemeCodeLabel]="individualIdSchemeCodeLabel()"
  [proprietaryIndividualIdSchemeFieldName]="
    proprietaryIndividualIdSchemeFieldName()
  "
  [proprietaryIndividualIdSchemeLabel]="proprietaryIndividualIdSchemeLabel()"
  [individualIdIssuerFieldName]="individualIdIssuerFieldName()"
  [individualIdIssuerLabel]="individualIdIssuerLabel()"
  [isReadOnly]="isReadOnly()"
/>

<!-- CtryOfRes -->
<app-country-code-input
  [fieldName]="countryOfResidenceFieldName()"
  [label]="countryOfResidenceLabel()"
  [isReadOnly]="isReadOnly()"
/>
