<app-one-of-selector
  header="Amount Type"
  i18n-header
  option1Label="Code"
  i18n-option1Label
  option2Label="Proprietary"
  i18n-option2Label
  [option1Fields]="[amountTypeCodeFieldName()]"
  [option2Fields]="[amountTypeProprietaryFieldName()]"
  [isReadOnly]="isReadOnly()"
>
  <ng-container slot="option1">
    <app-form-text-input
      [label]="amountTypeCodeLabel()"
      [fieldNames]="[amountTypeCodeFieldName()]"
      [isReadOnly]="isReadOnly()"
    />
  </ng-container>
  <ng-container slot="option2">
    <app-cbpr-restricted-finx-max-35-text-extended-input
      [fieldName]="amountTypeProprietaryFieldName()"
      [label]="amountTypeProprietaryLabel()"
      [isReadOnly]="isReadOnly()"
    />
  </ng-container>
</app-one-of-selector>
<app-active-or-historic-currency-and-amount-input
  [amountFieldName]="amountFieldName()"
  [amountLabel]="amountLabel()"
  [currencyFieldName]="currencyFieldName()"
  [currencyLabel]="currencyLabel()"
  [isReadOnly]="isReadOnly()"
/>
