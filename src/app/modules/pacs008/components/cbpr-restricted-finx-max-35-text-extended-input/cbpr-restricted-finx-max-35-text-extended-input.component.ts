import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormTextInputComponent } from '../../../shared/ui/form/form-fields/form-text-input/form-text-input.component';

@Component({
  selector: 'app-cbpr-restricted-finx-max-35-text-extended-input',
  imports: [FormTextInputComponent],
  templateUrl:
    './cbpr-restricted-finx-max-35-text-extended-input.component.html',
  styleUrl: './cbpr-restricted-finx-max-35-text-extended-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CBPR_RestrictedFINXMax35Text_ExtendedInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
