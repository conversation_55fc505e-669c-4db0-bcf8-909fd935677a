import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormTextInputComponent } from '../../../shared/ui/form/form-fields/form-text-input/form-text-input.component';

@Component({
  selector: 'app-clearing-system-identification-2-choice-1-input',
  imports: [FormTextInputComponent],
  templateUrl:
    './clearing-system-identification-2-choice-1-input.component.html',
  styleUrl: './clearing-system-identification-2-choice-1-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ClearingSystemIdentification2Choice__1InputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
