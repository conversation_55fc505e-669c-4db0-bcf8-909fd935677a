import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormTextInputComponent } from '../../../shared/ui/form/form-fields/form-text-input/form-text-input.component';

@Component({
  selector: 'app-active-or-historic-currency-code-input',
  imports: [FormTextInputComponent],
  templateUrl: './active-or-historic-currency-code-input.component.html',
  styleUrl: './active-or-historic-currency-code-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActiveOrHistoricCurrencyCodeInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
