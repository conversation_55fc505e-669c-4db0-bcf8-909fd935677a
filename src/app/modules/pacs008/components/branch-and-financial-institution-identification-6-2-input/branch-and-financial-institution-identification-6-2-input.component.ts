import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { BICFIDec2014IdentifierInputComponent } from '../bicfi-dec-2014-identifier-input';
import { ClearingSystemMemberIdentification2__1InputComponent } from '../clearing-system-member-identification-2-1-input';
import { LEIIdentifierInputComponent } from '../lei-identifier-input';

@Component({
  selector: 'app-branch-and-financial-institution-identification-6-2-input',
  imports: [
    BICFIDec2014IdentifierInputComponent,
    ClearingSystemMemberIdentification2__1InputComponent,
    LEIIdentifierInputComponent,
  ],
  templateUrl:
    './branch-and-financial-institution-identification-6-2-input.component.html',
  styleUrl:
    './branch-and-financial-institution-identification-6-2-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BranchAndFinancialInstitutionIdentification6__2InputComponent {
  bicfiFieldName = input.required<string>();
  bicfiLabel = input.required<string>();
  clearingSystemIdCodeFieldName = input.required<string>();
  clearingSystemIdCodeLabel = input.required<string>();
  clearingSystemMemberIdFieldName = input.required<string>();
  clearingSystemMemberIdLabel = input.required<string>();
  leiIdentifierFieldName = input.required<string>();
  leiIdentifierLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
