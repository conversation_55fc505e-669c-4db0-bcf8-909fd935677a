import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormTextInputComponent } from '../../../shared/ui/form/form-fields/form-text-input/form-text-input.component';

@Component({
  selector: 'app-external-organisation-identification-1-code-input',
  imports: [FormTextInputComponent],
  templateUrl:
    './external-organisation-identification-1-code-input.component.html',
  styleUrl: './external-organisation-identification-1-code-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ExternalOrganisationIdentification1CodeInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
