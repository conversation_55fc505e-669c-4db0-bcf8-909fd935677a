import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormTextInputComponent } from '../../../shared/ui/form/form-fields/form-text-input/form-text-input.component';

@Component({
  selector: 'app-external-person-identification-1-code-input',
  imports: [FormTextInputComponent],
  templateUrl: './external-person-identification-1-code-input.component.html',
  styleUrl: './external-person-identification-1-code-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ExternalPersonIdentification1CodeInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
