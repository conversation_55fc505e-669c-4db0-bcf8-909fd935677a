import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormTextInputComponent } from '../../../shared/ui/form/form-fields/form-text-input/form-text-input.component';

@Component({
  selector: 'app-country-code-input',
  imports: [FormTextInputComponent],
  templateUrl: './country-code-input.component.html',
  styleUrl: './country-code-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CountryCodeInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  isReadOnly = input.required<boolean>();
}
