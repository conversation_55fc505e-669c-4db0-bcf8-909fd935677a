import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormTextInputComponent } from '../../../shared/ui/form/form-fields/form-text-input/form-text-input.component';
import { ActiveOrHistoricCurrencyCodeInputComponent } from '../active-or-historic-currency-code-input';

@Component({
  selector: 'app-active-or-historic-currency-and-amount-input',
  imports: [FormTextInputComponent, ActiveOrHistoricCurrencyCodeInputComponent],
  templateUrl: './active-or-historic-currency-and-amount-input.component.html',
  styleUrl: './active-or-historic-currency-and-amount-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActiveOrHistoricCurrencyAndAmountInputComponent {
  amountFieldName = input.required<string>();
  amountLabel = input.required<string>();
  currencyFieldName = input.required<string>();
  currencyLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
