<app-one-of-selector
  header="Choose between an organisation or an individual"
  i18n-header
  option1Label="Organisation"
  i18n-option1Label
  option2Label="Individual"
  i18n-option2Label
  [option1Fields]="[
    bicFieldName(),
    leiFieldName(),
    organisationIdFieldName(),
    organisationIdSchemeCodeFieldName(),
    proprietaryOrganisationIdSchemeFieldName(),
    organisationIdIssuerFieldName()
  ]"
  [option2Fields]="[
    birthDateFieldName(),
    provinceOfBirthFieldName(),
    cityOfBirthFieldName(),
    countryOfBirthFieldName(),
    individualIdFieldName(),
    individualIdSchemeCodeFieldName(),
    proprietaryIndividualIdSchemeFieldName(),
    individualIdIssuerFieldName()
  ]"
  [isReadOnly]="isReadOnly()"
>
  <ng-container slot="option1">
    <app-any-bic-dec-2014-identifier-input
      [fieldName]="bicFieldName()"
      [label]="bicLabel()"
      [isReadOnly]="isReadOnly()"
    />
    <app-lei-identifier-input
      [fieldName]="leiFieldName()"
      [label]="leiLabel()"
      [isReadOnly]="isReadOnly()"
    />
    <!--TODO: This must be an array-->
    <app-hidden-section
      header="Other Identification"
      i18n-header
      [errorScopes]="[
        organisationIdFieldName(),
        organisationIdSchemeCodeFieldName(),
        proprietaryOrganisationIdSchemeFieldName(),
        organisationIdIssuerFieldName()
      ]"
    >
      <app-cbpr-restricted-finx-max-35-text-input
        [fieldName]="organisationIdFieldName()"
        [label]="organisationIdLabel()"
        [isReadOnly]="isReadOnly()"
      />
      <app-one-of-selector
        header="Name of the Identification Schema"
        i18n-header
        option1Label="Code"
        i18n-option1Label
        option2Label="Proprietary"
        i18n-option2Label
        [option1Fields]="[organisationIdSchemeCodeFieldName()]"
        [option2Fields]="[proprietaryOrganisationIdSchemeFieldName()]"
        [isReadOnly]="isReadOnly()"
      >
        <ng-container slot="option1">
          <app-external-organisation-identification-1-code-input
            [fieldName]="organisationIdSchemeCodeFieldName()"
            [label]="organisationIdSchemeCodeLabel()"
            [isReadOnly]="isReadOnly()"
          />
        </ng-container>
        <ng-container slot="option2">
          <app-cbpr-restricted-finx-max-35-text-input
            [fieldName]="proprietaryOrganisationIdSchemeFieldName()"
            [label]="proprietaryOrganisationIdSchemeLabel()"
            [isReadOnly]="isReadOnly()"
          />
        </ng-container>
      </app-one-of-selector>
      <app-cbpr-restricted-finx-max-35-text-input
        [fieldName]="organisationIdIssuerFieldName()"
        [label]="organisationIdIssuerLabel()"
        [isReadOnly]="isReadOnly()"
      />
    </app-hidden-section>
  </ng-container>
  <ng-container slot="option2">
    <app-date-and-place-of-birth-1-1-input
      [birthDateFieldName]="birthDateFieldName()"
      [birthDateLabel]="birthDateLabel()"
      [provinceOfBirthFieldName]="provinceOfBirthFieldName()"
      [provinceOfBirthLabel]="provinceOfBirthLabel()"
      [cityOfBirthFieldName]="cityOfBirthFieldName()"
      [cityOfBirthLabel]="cityOfBirthLabel()"
      [countryOfBirthFieldName]="countryOfBirthFieldName()"
      [countryOfBirthLabel]="countryOfBirthLabel()"
      [isReadOnly]="isReadOnly()"
    />
    <!--TODO: This must be an array-->
    <app-hidden-section
      header="Other Identification"
      i18n-header
      [errorScopes]="[
        individualIdFieldName(),
        individualIdSchemeCodeFieldName(),
        proprietaryIndividualIdSchemeFieldName(),
        individualIdIssuerFieldName()
      ]"
    >
      <app-cbpr-restricted-finx-max-35-text-input
        [fieldName]="individualIdFieldName()"
        [label]="individualIdLabel()"
        [isReadOnly]="isReadOnly()"
      />
      <app-one-of-selector
        header="Name of the Identification Schema"
        i18n-header
        option1Label="Code"
        i18n-option1Label
        option2Label="Proprietary"
        i18n-option2Label
        [option1Fields]="[individualIdSchemeCodeFieldName()]"
        [option2Fields]="[proprietaryIndividualIdSchemeFieldName()]"
        [isReadOnly]="isReadOnly()"
      >
        <ng-container slot="option1">
          <app-external-person-identification-1-code-input
            [fieldName]="individualIdSchemeCodeFieldName()"
            [label]="individualIdSchemeCodeLabel()"
            [isReadOnly]="isReadOnly()"
          />
        </ng-container>
        <ng-container slot="option2">
          <app-cbpr-restricted-finx-max-35-text-input
            [fieldName]="proprietaryIndividualIdSchemeFieldName()"
            [label]="proprietaryIndividualIdSchemeLabel()"
            [isReadOnly]="isReadOnly()"
          />
        </ng-container>
      </app-one-of-selector>
      <app-cbpr-restricted-finx-max-35-text-input
        [fieldName]="individualIdIssuerFieldName()"
        [label]="individualIdIssuerLabel()"
        [isReadOnly]="isReadOnly()"
      />
    </app-hidden-section>
  </ng-container>
</app-one-of-selector>
