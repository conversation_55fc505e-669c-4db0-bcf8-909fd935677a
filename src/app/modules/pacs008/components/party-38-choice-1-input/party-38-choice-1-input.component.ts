import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { OneOfSelectorComponent, HiddenSectionComponent } from '@shared/ui';
import { CBPR_RestrictedFINXMax35TextInputComponent } from '../cbpr-restricted-finx-max-35-text-input';
import { AnyBICDec2014IdentifierInputComponent } from '../any-bic-dec-2014-identifier-input';
import { LEIIdentifierInputComponent } from '../lei-identifier-input';
import { ExternalOrganisationIdentification1CodeInputComponent } from '../external-organisation-identification-1-code-input';
import { DateAndPlaceOfBirth1__1InputComponent } from '../date-and-place-of-birth-1-1-input';
import { ExternalPersonIdentification1CodeInputComponent } from '../external-person-identification-1-code-input';

@Component({
  selector: 'app-party-38-choice-1-input',
  imports: [
    OneOfSelectorComponent,
    CBPR_RestrictedFINXMax35TextInputComponent,
    AnyBICDec2014IdentifierInputComponent,
    LEIIdentifierInputComponent,
    HiddenSectionComponent,
    ExternalOrganisationIdentification1CodeInputComponent,
    DateAndPlaceOfBirth1__1InputComponent,
    ExternalPersonIdentification1CodeInputComponent,
  ],
  templateUrl: './party-38-choice-1-input.component.html',
  styleUrl: './party-38-choice-1-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Party38Choice__1InputComponent {
  bicFieldName = input.required<string>();
  bicLabel = input.required<string>();
  leiFieldName = input.required<string>();
  leiLabel = input.required<string>();
  organisationIdFieldName = input.required<string>();
  organisationIdLabel = input.required<string>();
  organisationIdSchemeCodeFieldName = input.required<string>();
  organisationIdSchemeCodeLabel = input.required<string>();
  proprietaryOrganisationIdSchemeFieldName = input.required<string>();
  proprietaryOrganisationIdSchemeLabel = input.required<string>();
  organisationIdIssuerFieldName = input.required<string>();
  organisationIdIssuerLabel = input.required<string>();
  birthDateFieldName = input.required<string>();
  birthDateLabel = input.required<string>();
  provinceOfBirthFieldName = input.required<string>();
  provinceOfBirthLabel = input.required<string>();
  cityOfBirthFieldName = input.required<string>();
  cityOfBirthLabel = input.required<string>();
  countryOfBirthFieldName = input.required<string>();
  countryOfBirthLabel = input.required<string>();
  individualIdFieldName = input.required<string>();
  individualIdLabel = input.required<string>();
  individualIdSchemeCodeFieldName = input.required<string>();
  individualIdSchemeCodeLabel = input.required<string>();
  proprietaryIndividualIdSchemeFieldName = input.required<string>();
  proprietaryIndividualIdSchemeLabel = input.required<string>();
  individualIdIssuerFieldName = input.required<string>();
  individualIdIssuerLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
