import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FinancialInstitutionIdentification18__1InputComponent } from '../financial-institution-identification-18-1-input';

@Component({
  selector: 'app-branch-and-financial-institution-identification-6-1-input',
  imports: [FinancialInstitutionIdentification18__1InputComponent],
  templateUrl:
    './branch-and-financial-institution-identification-6-1-input.component.html',
  styleUrl:
    './branch-and-financial-institution-identification-6-1-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BranchAndFinancialInstitutionIdentification6__1InputComponent {
  bicfiFieldName = input.required<string>();
  bicfiLabel = input.required<string>();
  clearingSystemIdCodeFieldName = input.required<string>();
  clearingSystemIdCodeLabel = input.required<string>();
  clearingSystemMemberIdFieldName = input.required<string>();
  clearingSystemMemberIdLabel = input.required<string>();
  leiIdentifierFieldName = input.required<string>();
  leiIdentifierLabel = input.required<string>();
  nameFieldName = input.required<string>();
  nameLabel = input.required<string>();
  departmentFieldName = input.required<string>();
  departmentLabel = input.required<string>();
  subDepartmentFieldName = input.required<string>();
  subDepartmentLabel = input.required<string>();
  streetNameFieldName = input.required<string>();
  streetNameLabel = input.required<string>();
  buildingNumberFieldName = input.required<string>();
  buildingNumberLabel = input.required<string>();
  buildingNameFieldName = input.required<string>();
  buildingNameLabel = input.required<string>();
  floorFieldName = input.required<string>();
  floorLabel = input.required<string>();
  postBoxFieldName = input.required<string>();
  postBoxLabel = input.required<string>();
  roomFieldName = input.required<string>();
  roomLabel = input.required<string>();
  postCodeFieldName = input.required<string>();
  postCodeLabel = input.required<string>();
  townNameFieldName = input.required<string>();
  townNameLabel = input.required<string>();
  townLocationNameFieldName = input.required<string>();
  townLocationNameLabel = input.required<string>();
  districtNameFieldName = input.required<string>();
  districtNameLabel = input.required<string>();
  countrySubdivisionFieldName = input.required<string>();
  countrySubdivisionLabel = input.required<string>();
  countryFieldName = input.required<string>();
  countryLabel = input.required<string>();
  addressLineFieldName = input.required<string>();
  addressLineLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
