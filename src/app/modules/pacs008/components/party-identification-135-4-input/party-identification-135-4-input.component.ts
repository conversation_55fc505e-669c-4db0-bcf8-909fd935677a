import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { CBPR_RestrictedFINXMax140Text_ExtendedInputComponent } from '../cbpr-restricted-finx-max-140-text-extended-input';
import { PostalAddress24__2InputComponent } from '../postal-address-24-2-input';
import { CountryCodeInputComponent } from '../country-code-input';
import { AnyBICDec2014IdentifierInputComponent } from '../any-bic-dec-2014-identifier-input';
import { LEIIdentifierInputComponent } from '../lei-identifier-input';
import { OneOfSelectorComponent, HiddenSectionComponent } from '@shared/ui';
import { CBPR_RestrictedFINXMax35Text_ExtendedInputComponent } from '../cbpr-restricted-finx-max-35-text-extended-input';
import { ExternalOrganisationIdentification1CodeInputComponent } from '../external-organisation-identification-1-code-input';
import { DateAndPlaceOfBirth1__1InputComponent } from '../date-and-place-of-birth-1-1-input';
import { ExternalPersonIdentification1CodeInputComponent } from '../external-person-identification-1-code-input';

@Component({
  selector: 'app-party-identification-135-4-input',
  imports: [
    CBPR_RestrictedFINXMax140Text_ExtendedInputComponent,
    PostalAddress24__2InputComponent,
    CountryCodeInputComponent,
    AnyBICDec2014IdentifierInputComponent,
    LEIIdentifierInputComponent,
    OneOfSelectorComponent,
    HiddenSectionComponent,
    CBPR_RestrictedFINXMax35Text_ExtendedInputComponent,
    ExternalOrganisationIdentification1CodeInputComponent,
    DateAndPlaceOfBirth1__1InputComponent,
    ExternalPersonIdentification1CodeInputComponent,
  ],
  templateUrl: './party-identification-135-4-input.component.html',
  styleUrl: './party-identification-135-4-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PartyIdentification135__4InputComponent {
  nameFieldName = input.required<string>();
  nameLabel = input.required<string>();

  departmentFieldName = input.required<string>();
  departmentLabel = input.required<string>();
  subDepartmentFieldName = input.required<string>();
  subDepartmentLabel = input.required<string>();
  streetNameFieldName = input.required<string>();
  streetNameLabel = input.required<string>();
  buildingNumberFieldName = input.required<string>();
  buildingNumberLabel = input.required<string>();
  buildingNameFieldName = input.required<string>();
  buildingNameLabel = input.required<string>();
  floorFieldName = input.required<string>();
  floorLabel = input.required<string>();
  postBoxFieldName = input.required<string>();
  postBoxLabel = input.required<string>();
  roomFieldName = input.required<string>();
  roomLabel = input.required<string>();
  postCodeFieldName = input.required<string>();
  postCodeLabel = input.required<string>();
  townNameFieldName = input.required<string>();
  townNameLabel = input.required<string>();
  townLocationNameFieldName = input.required<string>();
  townLocationNameLabel = input.required<string>();
  districtNameFieldName = input.required<string>();
  districtNameLabel = input.required<string>();
  countrySubdivisionFieldName = input.required<string>();
  countrySubdivisionLabel = input.required<string>();
  countryFieldName = input.required<string>();
  countryLabel = input.required<string>();
  addressLineFieldName = input.required<string>();
  addressLineLabel = input.required<string>();

  countryOfResidenceFieldName = input.required<string>();
  countryOfResidenceLabel = input.required<string>();

  bicFieldName = input.required<string>();
  bicLabel = input.required<string>();
  leiFieldName = input.required<string>();
  leiLabel = input.required<string>();
  organisationIdFieldName = input.required<string>();
  organisationIdLabel = input.required<string>();
  organisationIdSchemeCodeFieldName = input.required<string>();
  organisationIdSchemeCodeLabel = input.required<string>();
  proprietaryOrganisationIdSchemeFieldName = input.required<string>();
  proprietaryOrganisationIdSchemeLabel = input.required<string>();
  organisationIdIssuerFieldName = input.required<string>();
  organisationIdIssuerLabel = input.required<string>();
  birthDateFieldName = input.required<string>();
  birthDateLabel = input.required<string>();
  provinceOfBirthFieldName = input.required<string>();
  provinceOfBirthLabel = input.required<string>();
  cityOfBirthFieldName = input.required<string>();
  cityOfBirthLabel = input.required<string>();
  countryOfBirthFieldName = input.required<string>();
  countryOfBirthLabel = input.required<string>();
  individualIdFieldName = input.required<string>();
  individualIdLabel = input.required<string>();
  individualIdSchemeCodeFieldName = input.required<string>();
  individualIdSchemeCodeLabel = input.required<string>();
  proprietaryIndividualIdSchemeFieldName = input.required<string>();
  proprietaryIndividualIdSchemeLabel = input.required<string>();
  individualIdIssuerFieldName = input.required<string>();
  individualIdIssuerLabel = input.required<string>();

  isReadOnly = input.required<boolean>();
}
