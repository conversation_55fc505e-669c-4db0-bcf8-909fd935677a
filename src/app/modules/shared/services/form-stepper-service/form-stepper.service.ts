import { Injectable, signal } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { CompletionPageKey } from '@shared/types';

@Injectable({
  providedIn: 'root',
})
export class FormStepperService {
  #currentStepKey = signal<string | undefined>(undefined);

  currentStepKey = this.#currentStepKey.asReadonly();

  setCurrentStepKey(value: string | CompletionPageKey | undefined) {
    this.#currentStepKey.set(value);
  }

  goToPreviousStep(previousStepKey: string | undefined) {
    this.setCurrentStepKey(previousStepKey);
  }

  goToNextStep(nextStepKey: string | CompletionPageKey, formGroup: FormGroup) {
    for (const key in formGroup.controls) {
      const control = formGroup.get(key);

      control?.updateValueAndValidity();
      control?.markAsTouched();
    }
    if (formGroup.valid) {
      this.setCurrentStepKey(nextStepKey);
    }
  }
}
