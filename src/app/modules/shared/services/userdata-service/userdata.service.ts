import { Injectable } from '@angular/core';
import { User } from '@shared/types';
import { delay, Observable, of, ReplaySubject, share, tap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
// TODO: This should be generated via OpenAPI
export class UserDataService {
  private userData$: Observable<User> | undefined = undefined;

  getUserData(searchString: string) {
    if (!this.userData$) {
      this.userData$ = of<User>({
        name: '<PERSON>',
        address: {
          streetName: 'Main St',
          buildingNumber: '123',
          postalCode: '12345',
          townName: 'Anytown',
          country: 'USA',
        },
      }).pipe(
        delay(1000),
        tap(() => {
          //console.log('User data fetched from API');
        }),
        share({ connector: () => new ReplaySubject<User>(1) })
      );
    }

    return this.userData$;
  }
}
