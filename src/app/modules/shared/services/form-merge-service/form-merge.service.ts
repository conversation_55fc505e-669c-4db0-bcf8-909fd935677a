import { Injectable, signal } from '@angular/core';
import { FormArray, FormControl, FormGroup } from '@angular/forms';

/**
 * Store the individual FormGroup data for each page of a multi-step form.
 * The data is updated for every page change. This way, the review page can access the data.
 */
@Injectable({
  providedIn: 'root',
})
export class FormMergeService {
  #mergedFormData = signal<Record<string, FormGroup>>({});
  mergedFormData = this.#mergedFormData.asReadonly();

  /**
   * Store form data from a specific page
   */
  setPageData(pageKey: string, formGroup: FormGroup): void {
    const readonlyForm = this.createReadonlyForm(formGroup);

    // Merge all page data into a single flat object
    this.#mergedFormData.set({
      ...this.mergedFormData(),
      [pageKey]: readonlyForm,
    });
  }

  private createReadonlyForm(sourceForm: FormGroup): FormGroup {
    const controls: { [key: string]: FormControl | FormArray } = {};

    for (const key of Object.keys(sourceForm.controls)) {
      const control = sourceForm.get(key);

      if (control instanceof FormArray) {
        // Create disabled FormControl for each item
        const arrayControls = control.controls.map(
          (arrayControl) =>
            new FormControl({
              value: arrayControl.value,
              disabled: true,
            })
        );
        controls[key] = new FormArray(arrayControls);
      } else {
        // Handle regular FormControl
        controls[key] = new FormControl({
          value: control?.value,
          disabled: true,
        });
      }
    }

    return new FormGroup(controls);
  }
}
