import { effect, inject } from '@angular/core';
import { FormGroup, NonNullableFormBuilder } from '@angular/forms';
import { FormMergeService } from '../../../../services/form-merge-service/form-merge.service';
import { FormSchemaMap } from '@shared/types';
import { FormStepperService } from '@shared/services';
import { FormStep } from '../form-stepper';
import { Rule } from '@helaba/iso20022-lib/rules';
import { FormErrorsService } from '@helaba/iso20022-lib/error';

export abstract class FormPageComponent<
  S extends FormSchemaMap,
  V extends Record<keyof S, any>
> {
  protected readonly fb = inject(NonNullableFormBuilder);
  protected readonly formMergeService = inject(FormMergeService);
  protected readonly formStepperService = inject(FormStepperService);
  protected readonly formErrorsService = inject(FormErrorsService);

  abstract readonly pageKey: string;
  abstract readonly formGroup: FormGroup<S>;
  abstract readonly formSteps: FormStep<any>[];
  abstract readonly affectedFields: Record<string, string[]>;
  abstract readonly formFields: (keyof S)[];
  abstract readonly validationRules: Rule<string, undefined>[];

  constructor() {
    effect(() => {
      this.initializeErrorMessages();
    });
  }

  /**
   * Initialize the error messages - to be implemented by child components
   */
  protected abstract initializeErrorMessages(): void;
}
