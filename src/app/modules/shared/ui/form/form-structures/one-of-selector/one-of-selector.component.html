<app-base-field
  [label]="header()"
  [fieldNames]="allFields()"
  [showErrors]="showErrors()"
  [isReadOnly]="isReadOnly()"
>
  <div class="one-of-select-button">
    <app-select-button
      [options]="selectButtonOptions()"
      [defaultValue]="
        isReadOnly()
          ? hasFilledOutOption1Fields()
            ? OPTION1
            : hasFilledOutOption2Fields()
            ? OPTION2
            : null
          : null
      "
      [isReadOnly]="isReadOnly()"
      (onChange)="onChangeSelectedOption($event)"
    />
  </div>
  <div class="one-of-content">
    @if (selectedOption() === OPTION1) {
    <p-panel>
      <ng-content select="[slot='option1']" />
    </p-panel>
    } @if (selectedOption() === OPTION2) {
    <p-panel>
      <ng-content select="[slot='option2']" />
    </p-panel>
    } @if (isReadOnly() && !hasFilledOutOption1Fields() &&
    !hasFilledOutOption2Fields()) {
    <p-panel i18n> No option selected. </p-panel>
    }
  </div>
</app-base-field>
