import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  input,
} from '@angular/core';
import { FormStep } from './form-stepper.types';
import { StepperModule } from 'primeng/stepper';
import { ButtonModule } from 'primeng/button';
import { NgComponentOutlet } from '@angular/common';
import { FormStepperService } from '@shared/services';

// TODO: Step back from review-submit page

@Component({
  selector: 'app-form-stepper',
  imports: [NgComponentOutlet, StepperModule, ButtonModule],
  templateUrl: './form-stepper.component.html',
  styleUrl: './form-stepper.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormStepperComponent<TSteps extends readonly FormStep<any>[]> {
  steps = input.required<TSteps>();

  formStepperService = inject(FormStepperService);

  constructor() {
    effect(() => {
      this.formStepperService.setCurrentStepKey(
        this.steps().length > 0 ? this.steps()[0].key : undefined
      );
    });
  }

  currentValue = computed(() => {
    const currentStepKey = this.formStepperService.currentStepKey();
    if (!currentStepKey) {
      return 1;
    }
    return this.steps().findIndex((step) => step.key === currentStepKey) + 1;
  });
}
