<p-stepper [value]="currentValue()" [linear]="true">
  <p-step-list>
    @for (step of steps(); let index = $index; track step.key) {
    <p-step [value]="index + 1">{{ step.item.label }}</p-step>
    }
  </p-step-list>
  <p-step-panels>
    @for (step of steps(); let index = $index; track step.key) {
    <p-step-panel [value]="index + 1">
      <ng-template #content>
        <ng-container [ngComponentOutlet]="step.component"></ng-container>
      </ng-template>
    </p-step-panel>
    }
  </p-step-panels>
</p-stepper>
