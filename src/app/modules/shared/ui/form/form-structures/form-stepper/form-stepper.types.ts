import { MenuItem } from 'primeng/api';
import { FormPageComponent } from '../form-page';
import { FormPageGenerics } from '../form-page/form-page.types';
import { Type } from '@angular/core';
import { ReviewSubmitPageComponent } from '../review-submit-page';

export type FormStep<T extends FormPageGenerics<any, any>> = {
  key: string;
  item: MenuItem;
  component:
    | Type<FormPageComponent<T['FormSchema'], T['FormValues']>>
    | Type<ReviewSubmitPageComponent>;
};
