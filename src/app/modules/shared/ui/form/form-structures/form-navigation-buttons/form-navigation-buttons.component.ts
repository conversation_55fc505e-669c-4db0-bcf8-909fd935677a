import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  output,
} from '@angular/core';
import { FormMergeService, FormStepperService } from '@shared/services';
import { ButtonModule } from 'primeng/button';
import { FormStep } from '../form-stepper';
import { CompletionPageKey } from '@shared/types';
import { FormGroup } from '@angular/forms';

@Component({
  selector: 'app-form-navigation-buttons',
  imports: [ButtonModule],
  templateUrl: './form-navigation-buttons.component.html',
  styleUrl: './form-navigation-buttons.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormNavigationButtonsComponent<
  TSteps extends readonly FormStep<any>[]
> {
  steps = input.required<TSteps>();
  formGroup = input.required<FormGroup>();

  formStepperService = inject(FormStepperService);
  formMergeService = inject(FormMergeService);

  numberOfSteps = computed(() => this.steps().length);
  currentStepKey = computed(() => this.formStepperService.currentStepKey());
  currentStepIndex = computed(() => {
    const currentStepKey = this.currentStepKey();
    if (!currentStepKey) {
      return 0; // Default to 0 if no current step key is set
    }
    return this.steps().findIndex((step) => step.key === currentStepKey);
  });

  onBack() {
    this.saveFormData();

    const currentStepIndex = this.currentStepIndex();
    if (currentStepIndex <= 0) {
      console.warn('No previous step available, already at the first step.');
      return;
    }

    const previousStepKey = this.steps()[currentStepIndex - 1].key;
    this.formStepperService.goToPreviousStep(previousStepKey);
  }

  onNext() {
    this.saveFormData();

    const currentStepIndex = this.currentStepIndex();

    if (currentStepIndex >= this.numberOfSteps() - 1) {
      console.warn('No next step available, already at the last step.');
      return;
    }

    const nextStepKey = this.steps()[currentStepIndex + 1].key;
    // While we always allow to go back, going forward involves validation of the current page's inputs for which the FormGroup instance is required.
    this.formStepperService.goToNextStep(nextStepKey, this.formGroup());
  }

  private saveFormData() {
    const currentStepKey = this.currentStepKey();
    if (!currentStepKey) {
      console.warn('No current step key set, cannot save form data.');
      return;
    }
    if (currentStepKey === CompletionPageKey.COMPLETION) {
      return;
    }
    this.formMergeService.setPageData(currentStepKey, this.formGroup());
  }
}
