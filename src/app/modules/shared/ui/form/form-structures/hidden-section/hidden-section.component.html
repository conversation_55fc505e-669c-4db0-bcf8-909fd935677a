<div class="hiddenSectionContainer">
  <p-panel
    [header]="header()"
    [toggleable]="true"
    [collapsed]="isCollapsed()"
    [class]="
      clsx('hiddenSection', {
        hiddenSectionExpanded: !isCollapsed(),
        hiddenSectionCollapsed: isCollapsed(),
        hiddenSectionError: hasError() && isCollapsed()
      })
    "
    (collapsedChange)="onCollapsedChange($event)"
    toggler="header"
  >
    <ng-content></ng-content>
  </p-panel>
</div>
