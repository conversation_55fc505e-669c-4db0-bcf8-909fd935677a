import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  TemplateRef,
} from '@angular/core';
import { BaseFieldComponent } from '../base-field';
import {
  AbstractControl,
  ControlContainer,
  FormArray,
  FormGroup,
  FormGroupDirective,
  NonNullableFormBuilder,
  ReactiveFormsModule,
} from '@angular/forms';
import { FormValueComponent } from '../form-value';
import { ButtonModule } from 'primeng/button';
import { NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-form-group-array',
  imports: [
    ReactiveFormsModule,
    BaseFieldComponent,
    FormV<PERSON>ueComponent,
    ButtonModule,
    NgTemplateOutlet,
  ],
  templateUrl: './form-group-array.component.html',
  styleUrl: './form-group-array.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormGroupArrayComponent extends BaseFieldComponent {
  controlContainer = inject(ControlContainer);
  fb = inject(NonNullableFormBuilder);

  // Factory function to create new form groups
  groupFactory = input.required<() => FormGroup>();
  // Template for rendering each group item
  itemTemplate = input.required<TemplateRef<unknown>>();

  get formGroup(): FormGroup {
    return this.controlContainer.control as FormGroup;
  }

  fieldName = computed(() => {
    const fieldNames = this.fieldNames();
    if (fieldNames.length > 1) {
      throw new Error(
        'FormGroupArrayComponent can only handle a single field name.'
      );
    }
    return fieldNames[0];
  });

  formArray = computed(() => {
    const field = this.formGroup.get(this.fieldName());
    if (field && field instanceof FormArray) {
      return field;
    }
    return null;
  });

  controls = computed(() => {
    const array = this.formArray();
    return array ? array.controls : [];
  });

  addItem() {
    const fieldArray = this.formArray();
    if (fieldArray) {
      const newGroup = this.groupFactory()();
      fieldArray.push(newGroup);
    }
  }

  removeItem(index: number) {
    const fieldArray = this.formArray();
    if (fieldArray) {
      fieldArray.removeAt(index);
    }
  }
}
