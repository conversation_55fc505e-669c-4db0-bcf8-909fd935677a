<app-base-field
  [label]="label()"
  [fieldNames]="fieldNames()"
  [isReadOnly]="isReadOnly()"
  [useFieldset]="true"
>
  @if (isReadOnly()) {
  <app-form-value [fieldName]="fieldName()" />
  } @else {
  <div [formArrayName]="fieldName()">
    @for (control of controls(); let index = $index; track index;) {
    <div [formGroupName]="index">
      <div>
        <h4>Item {{ index + 1 }}</h4>
        <p-button
          icon="pi pi-times"
          [rounded]="true"
          [text]="true"
          severity="danger"
          (click)="removeItem(index)"
        />
      </div>

      <div>
        <!-- Custom template content -->
        <ng-container
          *ngTemplateOutlet="
            itemTemplate();
            context: {
              $implicit: index,
              control,
              fieldPrefix: `${fieldName()}-#${index}`
            }
          "
        />
      </div>
    </div>
    }
  </div>
  <p-button
    icon="pi pi-plus"
    (click)="addItem()"
    label="Add Item"
    i18n-label
  />}
</app-base-field>
