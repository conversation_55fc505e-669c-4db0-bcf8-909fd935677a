import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
  output,
} from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { BaseFieldComponent } from '../base-field';
import { InputNumberInputEvent, InputNumberModule } from 'primeng/inputnumber';
import { FormValueComponent } from '../form-value';

@Component({
  selector: 'app-form-number-input',
  imports: [
    ReactiveFormsModule,
    BaseFieldComponent,
    InputNumberModule,
    FormValueComponent,
  ],
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree.
  ],
  templateUrl: './form-number-input.component.html',
  styleUrl: './form-number-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormNumberInputComponent extends BaseFieldComponent {
  max = input<number | null>(null);
  maxFractionDigits = input<number>(0);
  onChange = output<number | null>();

  fieldName = computed(() => {
    const fieldNames = this.fieldNames();
    if (fieldNames.length > 1) {
      throw new Error(
        'FormNumberInputComponent can only handle a single field name.'
      );
    }
    return fieldNames[0];
  });

  onInput($event: InputNumberInputEvent) {
    const value = $event.value;
    this.onChange.emit(
      value === null
        ? null
        : typeof value === 'string'
        ? parseFloat(value)
        : value
    );
  }
}
