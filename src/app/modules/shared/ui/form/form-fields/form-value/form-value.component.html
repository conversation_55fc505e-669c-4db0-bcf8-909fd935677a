@if (controlData(); as controlData) {
<!-- Handle FormArray -->
@if (controlData.type === 'array') {
<div class="formArrayContainer" [attr.data-level]="level()">
  @if (controlData.length > 0) {

  <div class="arrayCount" i18n>{{ controlData.length }} items</div>

  } @if (controlData.length === 0) {
  <div class="emptyArray" i18n>No items</div>
  } @else {
  <div class="formArrayItems">
    @for (control of controlData.controls; let index = $index; track index) {
    <div class="formArrayItem" [attr.data-index]="index">
      <div class="itemHeader">
        <span class="itemIndex" i18n>Item {{ index + 1 }}</span>
      </div>

      <!-- Recursively handle the array item -->
      @if (isLeafControl(control)) {
      <!-- Direct string value in array -->
      <input
        type="text"
        pInputText
        [value]="control.value"
        readonly
        class="formValue leafValue"
      />
      } @else if (isFormControlWithObjectValue(control)) {
      <!-- FormControl with object value - display each property -->
      <div class="formGroupContainer">
        @for (key of objectKeys(control.value || {}); track key) {
        <div class="formField">
          <label class="fieldLabel">{{ key }}</label>
          <input
            type="text"
            pInputText
            [value]="control.value[key] || ''"
            readonly
            class="formValue leafValue"
          />
        </div>
        }
      </div>
      } @else if (isFormGroup(control)) {
      <!-- FormGroup in array - display all its fields -->
      <div class="formGroupContainer">
        @for (key of objectKeys((control).controls); track key) {
        <div class="formField">
          <label class="fieldLabel">{{ key }}</label>
          <app-form-value
            [fieldName]="`${fieldName()}.${index}.${key}`"
            [level]="level() + 2"
          />
        </div>
        }
      </div>
      } @else if (isFormArray(control)) {
      <!-- Nested FormArray -->
      <app-form-value
        [fieldName]="`${fieldName()}.${index}`"
        [level]="level() + 1"
      />
      }
    </div>
    }
  </div>
  }
</div>
}

<!-- Handle FormGroup -->
@else if (controlData.type === 'group') {
<div class="formGroupContainer" [attr.data-level]="level()">
  @for (key of controlData.keys; track key) {
  <div class="formField">
    <label class="fieldLabel">{{ key }}</label>
    <app-form-value
      [fieldName]="`${fieldName()}.${key}`"
      [level]="level() + 1"
    />
  </div>
  }
</div>
}

<!-- Handle FormControl (leaf node) -->
@else if (controlData.type === 'control') {
<input
  type="text"
  pInputText
  [value]="controlData.value || ''"
  readonly
  class="formValue leafValue"
  [attr.data-level]="level()"
/>
} } @else {
<!-- Fallback for undefined controls -->
<div class="noValue">No value</div>
}

<!-- @if (controls() !== undefined) {
<div [formArrayName]="fieldName()">
  @for (entry of controls(); let index = $index; track index;) {
  <div>
    <input
      type="text"
      pInputText
      [id]="fieldName()"
      [formControlName]="index"
      readonly
      class="form-value"
    />
  </div>
  }
</div>
} @else {
<input
  type="text"
  pInputText
  [id]="fieldName()"
  [formControlName]="fieldName()"
  readonly
  class="form-value"
/>
} -->
