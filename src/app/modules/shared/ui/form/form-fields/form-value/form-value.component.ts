import { JsonPipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
} from '@angular/core';
import {
  AbstractControl,
  ControlContainer,
  FormArray,
  FormControl,
  FormGroup,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';

@Component({
  selector: 'app-form-value',
  imports: [ReactiveFormsModule, InputTextModule, JsonPipe],
  templateUrl: './form-value.component.html',
  styleUrl: './form-value.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormValueComponent {
  fieldName = input.required<string>();
  level = input<number>(0); // For indentation/styling purposes

  controlContainer = inject(ControlContainer);

  objectKeys = Object.keys;

  get formGroup(): FormGroup {
    return this.controlContainer.control as FormGroup;
  }

  fieldControl = computed(() => {
    return this.formGroup.get(this.fieldName());
  });

  // Determine the type of control and return structured data
  controlData = computed(() => {
    const control = this.fieldControl();
    if (!control) return null;

    if (control instanceof FormArray) {
      return {
        type: 'array',
        controls: control.controls,
        length: control.length,
      } as const;
    } else if (control instanceof FormGroup) {
      return {
        type: 'group',
        controls: control.controls,
        keys: Object.keys(control.controls),
      } as const;
    } else if (control instanceof FormControl) {
      return {
        type: 'control',
        value: control.value,
      } as const;
    }

    return null;
  });

  isLeafControl(
    control: AbstractControl
  ): control is FormControl<string | null | undefined> {
    return (
      control instanceof FormControl &&
      (control.value === null ||
        control.value === undefined ||
        typeof control.value === 'string')
    );
  }

  isFormControlWithObjectValue(
    control: AbstractControl
  ): control is FormControl<Record<string, unknown>> {
    return (
      control instanceof FormControl &&
      control.value !== null &&
      control.value !== undefined &&
      typeof control.value === 'object' &&
      !Array.isArray(control.value)
    );
  }

  isFormArray(control: AbstractControl): control is FormArray {
    return control instanceof FormArray;
  }

  isFormGroup(control: AbstractControl): control is FormGroup {
    return control instanceof FormGroup;
  }

  // TODO: Remove
  getControls(control: AbstractControl) {
    return 'controls' in control ? control['controls'] : 'no controls';
  }
}
