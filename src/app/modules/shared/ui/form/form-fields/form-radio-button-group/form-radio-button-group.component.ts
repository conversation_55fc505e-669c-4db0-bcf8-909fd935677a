import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { BaseFieldComponent } from '../base-field';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SelectOption } from '../form-select';
import { FormValueComponent } from '../form-value';

@Component({
  selector: 'app-form-radio-button-group',
  imports: [
    ReactiveFormsModule,
    BaseFieldComponent,
    RadioButtonModule,
    FormValueComponent,
  ],
  templateUrl: './form-radio-button-group.component.html',
  styleUrl: './form-radio-button-group.component.css',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormRadioButtonGroupComponent<
  T extends string
> extends BaseFieldComponent {
  options = input.required<SelectOption<T>[]>();

  fieldName = computed(() => {
    const fieldNames = this.fieldNames();
    if (fieldNames.length > 1) {
      throw new Error(
        'FormRadioButtonGroupComponent can only handle a single field name.'
      );
    }
    return fieldNames[0];
  });
}
