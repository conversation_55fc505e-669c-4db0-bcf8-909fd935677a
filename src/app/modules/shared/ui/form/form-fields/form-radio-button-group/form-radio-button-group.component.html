<app-base-field
  [label]="label()"
  [fieldNames]="[fieldName()]"
  [isReadOnly]="isReadOnly()"
  [useFieldset]="true"
>
  @if (isReadOnly()) {
  <app-form-value [fieldName]="fieldName()" />
  } @else {
  <div class="radio-button-group">
    @for (option of options(); track $index) {
    <div class="radio-element">
      <p-radiobutton
        [inputId]="`${fieldName()}-${option.value}`"
        [value]="option.value"
        [formControlName]="fieldName()"
      />
      <label [for]="`${fieldName()}-${option.value}`">{{ option.label }}</label>
    </div>
    }
  </div>
  }
</app-base-field>
