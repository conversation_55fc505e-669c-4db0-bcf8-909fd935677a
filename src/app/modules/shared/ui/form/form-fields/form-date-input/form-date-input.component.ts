import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { BaseFieldComponent } from '../base-field';
import { DatePickerModule } from 'primeng/datepicker';
import { FormValueComponent } from '../form-value';

@Component({
  selector: 'app-form-date-input',
  imports: [
    ReactiveFormsModule,
    BaseFieldComponent,
    DatePickerModule,
    FormValueComponent,
  ],
  templateUrl: './form-date-input.component.html',
  styleUrl: './form-date-input.component.css',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormDateInputComponent extends BaseFieldComponent {
  showTime = input<boolean>(false);
  timeOnly = input<boolean>(false);

  fieldName = computed(() => {
    const fieldNames = this.fieldNames();
    if (fieldNames.length > 1) {
      throw new Error(
        'FormDateInputComponent can only handle a single field name.'
      );
    }
    return fieldNames[0];
  });
}
