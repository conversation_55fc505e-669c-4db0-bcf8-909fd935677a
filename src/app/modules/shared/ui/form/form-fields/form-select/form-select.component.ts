import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { BaseFieldComponent } from '../base-field';
import { SelectModule } from 'primeng/select';
import { SelectOption } from './form-select.types';
import { FormValueComponent } from '../form-value';

@Component({
  selector: 'app-form-select',
  imports: [
    ReactiveFormsModule,
    BaseFieldComponent,
    SelectModule,
    FormValueComponent,
  ],
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree.
  ],
  templateUrl: './form-select.component.html',
  styleUrl: './form-select.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormSelectComponent<T> extends BaseFieldComponent {
  options = input.required<SelectOption<T>[]>();
  placeholder = input.required<string>();
  editable = input<boolean>(false);
  filter = input<boolean>(false);

  fieldName = computed(() => {
    const fieldNames = this.fieldNames();
    if (fieldNames.length > 1) {
      throw new Error(
        'FormSelectComponent can only handle a single field name.'
      );
    }
    return fieldNames[0];
  });
}
