import { ChangeDetectionStrategy, Component, computed } from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { BaseFieldComponent } from '../base-field';
import { InputTextModule } from 'primeng/inputtext';
import { FormValueComponent } from '../form-value';

@Component({
  selector: 'app-form-text-input',
  imports: [
    ReactiveFormsModule,
    BaseFieldComponent,
    InputTextModule,
    FormValueComponent,
  ],
  templateUrl: './form-text-input.component.html',
  styleUrl: './form-text-input.component.css',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormTextInputComponent extends BaseFieldComponent {
  fieldName = computed(() => {
    const fieldNames = this.fieldNames();
    if (fieldNames.length > 1) {
      throw new Error(
        'FormTextInputComponent can only handle a single field name.'
      );
    }
    return fieldNames[0];
  });
}
