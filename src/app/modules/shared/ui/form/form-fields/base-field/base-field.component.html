<div class="form-field">
  @if (fieldNames().length > 1) {
  <p-fieldset [legend]="label()" class="form-field-fieldset">
    <div>
      <ng-container *ngTemplateOutlet="projectedContent"></ng-container>
    </div>
    @if (showErrors()) {
    <div>
      @for (fieldName of fieldNames(); track fieldName) {
      <app-form-field-errors [fieldName]="fieldName" />
      }
    </div>
    }
  </p-fieldset>
  } @if (fieldNames().length === 1) { @if (useFieldset()) {
  <app-plain-fieldset [legend]="label()">
    <div>
      <ng-container *ngTemplateOutlet="projectedContent"></ng-container>
    </div>
  </app-plain-fieldset>
  } @else {
  <label [for]="fieldNames()[0]" class="form-field-label">{{ label() }}</label>
  }

  <div>
    <ng-container *ngTemplateOutlet="projectedContent"></ng-container>
  </div>
  <div>
    <app-form-field-errors [fieldName]="fieldNames()[0]" />
  </div>
  }
</div>

<!-- There must only be one 'ng-content' in the template if we don't want to use slots which should not be necessary in this case as the two branches are mutually exclusive. -->
<ng-template #projectedContent>
  <ng-content></ng-content>
</ng-template>
