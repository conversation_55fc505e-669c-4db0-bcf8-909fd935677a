import { NgTemplateOutlet } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
} from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { FormErrorsService } from '@helaba/iso20022-lib/error';
import { FieldsetModule } from 'primeng/fieldset';
import { FormFieldErrorsComponent } from '@shared/ui';
import { PlainFieldsetComponent } from '../../form-structures/plain-fieldset/plain-fieldset.component'; // For some weird reason, the barrel files don't work for this one import.
@Component({
  selector: 'app-base-field',
  imports: [
    ReactiveFormsModule,
    FieldsetModule,
    NgTemplateOutlet,
    FormFieldErrorsComponent,
    PlainFieldsetComponent,
  ],
  templateUrl: './base-field.component.html',
  styleUrl: './base-field.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BaseFieldComponent {
  label = input.required<string>();
  fieldNames = input.required<string[]>(); // We allow multiple field names such that we can use this component for error display for OneOfSelector components while no option is selected. All children fields that only allow for a single field name should throw an error if more than one field name is provided.
  isReadOnly = input.required<boolean>();
  showErrors = input<boolean>(true);
  useFieldset = input<boolean>(false); // Use a fieldset with a legend instead of a label even for cases with only one field name. E.g. a radio button group has multiple inputs but only one field name which cannot be used in the "for" attribute of the label as it would refer to none of the inputs.

  formErrorsService = inject(FormErrorsService);

  protected hasError = computed<boolean>(() => {
    if (this.isReadOnly()) {
      return false;
    }

    const erroneousScopes = this.formErrorsService.erroneousScopes();

    for (const fieldName of this.fieldNames()) {
      if (erroneousScopes.has(fieldName)) {
        return true;
      }
    }
    return false;
  });
}
