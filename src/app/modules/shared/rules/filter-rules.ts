import {
  BasicRule,
  Condition,
  isCondition,
  NestedCondition,
  Rule,
} from '@helaba/iso20022-lib/rules';

/**
 * Filter the rules down to those that are relevant to the given fields, i.e. those where the target and condition only reference relevant fields.
 * @param allRules all rules to filter
 * @param relevantFields fields that are relevant for the rules
 * @returns filtered rules that are relevant to the given fields
 */
export function filterRules(
  allRules: Rule[],
  relevantFields: string[]
): Rule[] {
  return allRules.filter((rule) => {
    if (rule.type === 'serverOnly') {
      return false; // 'serverOnly' rules should not even come through here but this helps with typing
    }
    if (rule.type === 'condition') {
      const allConditionsReferenceRelevantFields = rule.conditions.every(
        (condition: NestedCondition) =>
          isCondition(condition)
            ? conditionOnlyReferencesRelevantFields(condition, relevantFields)
            : condition.conditions.every((nestedCondition) =>
                conditionOnlyReferencesRelevantFields(
                  nestedCondition,
                  relevantFields
                )
              )
      );

      if (!allConditionsReferenceRelevantFields) {
        return false;
      }
      return rule.rules.every((subRule) =>
        basicRuleOnlyReferencesRelevantFields(subRule, relevantFields)
      );
    }

    return basicRuleOnlyReferencesRelevantFields(rule, relevantFields);
  });
}

function conditionOnlyReferencesRelevantFields(
  condition: Condition,
  relevantFields: string[]
): boolean {
  const mainFieldIsRelevant = relevantFields.includes(condition.field);

  if (condition.type === 'notEqual') {
    const otherFieldIsRelevant = relevantFields.includes(condition.otherField);
    return mainFieldIsRelevant && otherFieldIsRelevant;
  }

  return mainFieldIsRelevant;
}

function basicRuleOnlyReferencesRelevantFields(
  rule: BasicRule,
  relevantFields: string[]
): boolean {
  const targetIsRelevant = relevantFields.includes(rule.target);

  if (rule.type === 'contains') {
    const otherFieldsAreRelevant = rule.value.every((field) =>
      relevantFields.includes(field)
    );
    return targetIsRelevant && otherFieldsAreRelevant;
  }

  return targetIsRelevant;
}
