import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { Rule } from '@helaba/iso20022-lib/rules';

export type FormSchemaMap = Record<
  string,
  FormControl<string> | FormArray<FormControl<string>> | FormArray<FormGroup>
>;

// Generic utility type that extracts the value type from FormControls
export type FormValues<T> = {
  [K in keyof T]: T[K] extends FormControl<infer U>
    ? U
    : T[K] extends FormArray<FormControl<infer U>>
    ? U[]
    : never;
};

export enum CompletionPageKey {
  COMPLETION = 'completion',
}

export type FormPageMetadata = {
  formFields: string[];
  validationRules: Rule<string, undefined>[]; // We exclude 'description' to keep the file size of the metadata smaller.
  errorMessages: Record<string, string>;
  labels: Record<string, string>;
};

export type FormMetadata = {
  [pageKey: string]: FormPageMetadata;
};
