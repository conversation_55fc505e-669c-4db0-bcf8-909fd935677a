// TODO: This will be generated directly from the JSON schema and sits in the form-kit library

export const priority2Codes = ['HIGH', 'NORM'] as const;
export type Priority2Code = (typeof priority2Codes)[number];

export const externalServiceLevel1Codes = ['URGP', 'SEPA', 'NURG'] as const;
export type ExternalServiceLevel1Code =
  (typeof externalServiceLevel1Codes)[number];

export const externalCategoryPurpose1Codes = [
  'SUPP',
  'SALA',
  'PENS',
  'SECU',
  'RPRE',
] as const;
export type ExternalCategoryPurpose1Code =
  (typeof externalCategoryPurpose1Codes)[number];

export const externalPurpose1Codes = [
  'IVPT',
  'DIVI',
  'FEES',
  'LOAR',
  'GDSV',
] as const;
export type ExternalPurpose1Code = (typeof externalPurpose1Codes)[number];

export const activeCurrencyCodes = [
  'EUR',
  'USD',
  'GBP',
  'CHF',
  'AUD',
  'CAD',
  'NZD',
  'JPY',
  'CNY',
  'SEK',
  'NOK',
  'DKK',
  'SGD',
  'HKD',
  'MXN',
  'BRL',
  'ZAR',
  'INR',
  'RUB',
  'TRY',
  'PLN',
  'CZK',
  'HUF',
  'THB',
  'MYR',
  'PHP',
  'IDR',
];

export type ActiveCurrencyCode = (typeof activeCurrencyCodes)[number];

export const chargeBearerType1Codes = ['DEBT', 'CRED', 'SHAR'] as const;
export type ChargeBearerType1Code = (typeof chargeBearerType1Codes)[number];

export const settlementMethod1Codes = ['INDA', 'INGA', 'COVE'] as const;
export type SettlementMethod1Code = (typeof settlementMethod1Codes)[number];

export const priority3Codes = ['URGT', 'HIGH', 'NORM'] as const;
export type Priority3Code = (typeof priority3Codes)[number];

export const clearingChannel2Codes = ['RTGS', 'RTNS', 'MPNS', 'BOOK'] as const;
export type ClearingChannel2Code = (typeof clearingChannel2Codes)[number];

export const creditDebitCodes = ['CRDT', 'DBIT'] as const;
export type CreditDebitCode = (typeof creditDebitCodes)[number];

export const regulatoryReportingType1Codes = ['CRED', 'DEBT', 'BOTH'] as const;
export type RegulatoryReportingType1Code =
  (typeof regulatoryReportingType1Codes)[number];
